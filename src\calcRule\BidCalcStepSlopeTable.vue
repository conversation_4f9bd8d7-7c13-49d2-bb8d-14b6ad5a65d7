<template>
<el-row class="bcom-header">
    <el-row class="title">
        <el-text size="large" tag="b">放坡系数配置</el-text>
    </el-row>
</el-row>
    <vxe-toolbar>
        <template #buttons>
            <el-select v-model="currentArea"  placeholder="地区" filterable  @change="areaChange">
                <el-option v-for="area in allAreas" :key="area" :label="(configAreas?.includes(area)?area+'  &#10003;':area)" :value="area" />
            </el-select>
            <vxe-button icon="vxe-icon-square-plus" @click="newRow" :disabled="!currentArea" >新增</vxe-button>

            <el-popover placement="right" :visible="opShow"  width="460px" title="复制数据" popper-class="copy-area-prop" >
                <template #reference>
                    <el-button text icon="CopyDocument" type="primary" @click="opShow=true">复制数据</el-button>
                </template>
                <el-text>从</el-text>
                <el-text type="info">{{ currentArea }} </el-text>
                <el-text>复制到</el-text>
                <el-select placeholder="选择地区" size="small" v-model="opArea" :teleported="false" style="width: 100px;">
                    <el-option v-for="area in allAreas" :label="area" :value="area" />
                </el-select>
                <el-button type="primary" @click="copyData">确定</el-button>
                <el-button type="primary" @click="opShow = false">取消</el-button>
            </el-popover>
        </template>
    </vxe-toolbar>
    <vxe-table ref="attrsTable" height="900"  keep-source :scroll-y="{enabled:true,gt:20}"
        :edit-config="{ trigger: 'dblclick',showStatus:true,  mode: 'row' ,autoClear:false}" 
        @edit-closed="editClosed"
        :row-config="{ useKey: true, isHover: true,  keyField: 'id', transform: true ,height:'auto'}" :loading="tableLoading" :data="tbData">
        <vxe-column title="#" width="60" type="seq" />
        <vxe-column title="土质条件" width="160" field="soil_category"  :edit-render="{ autofocus: '.el-select'}"  :formatter="formatSoilCategory" >
            <template #edit="{ row }">
                <el-select  v-model="row.soil_category" placeholder="土质条件" :reserve-keyword="false" :teleported="false">
                    <el-option v-for="sc in soilCategories" :label="sc.label" :value="sc.value" />
                </el-select>
            </template>
        </vxe-column>
        <vxe-column title="起点深度(mm)"  :edit-render="{ autofocus: '.vxe-input--inner' }" >
            <template #default="{ row }">
                {{ `${row.start_depth_start??'' } ~ ${row.start_depth_end??''}` }}
            </template>
            <template #edit="{ row }">
                <el-row>
                    <vxe-input type="number" v-model="row.start_depth_start" :onkeypress="keydownEnter" clearable style="width: 42%;" ></vxe-input>
                    <el-text>~</el-text>
                    <vxe-input type="number" v-model="row.start_depth_end" :onkeypress="keydownEnter" clearable style="width: 42%;" ></vxe-input>
                </el-row>
            </template>
        </vxe-column>
        <vxe-column title="人工挖土" field="slope_factor_artificial"  :edit-render="{ autofocus: '.vxe-input--inner' }" >
            <template #edit="{ row }">
                <vxe-input type="float" v-model="row.slope_factor_artificial" :onkeypress="keydownEnter" clearable ></vxe-input>
            </template>
        </vxe-column>
        <vxe-column title="机械-基坑内" field="slope_factor_pit_inside"  :edit-render="{ autofocus: '.vxe-input--inner' }" >
            <template #edit="{ row }">
                <vxe-input type="float"  v-model="row.slope_factor_pit_inside" :onkeypress="keydownEnter" clearable  ></vxe-input>
            </template>
        </vxe-column>
        <vxe-column title="机械-基坑上" field="slope_factor_pit_top"  :edit-render="{ autofocus: '.vxe-input--inner' }" >
            <template #edit="{ row }">
                <vxe-input type="float" v-model="row.slope_factor_pit_top" :onkeypress="keydownEnter" clearable ></vxe-input>
            </template>
        </vxe-column>
        <vxe-column title="机械-沟槽上" field="slope_factor_groove_top"  :edit-render="{ autofocus: '.vxe-input--inner' }" >
            <template #edit="{ row }">
                <vxe-input type="float" v-model="row.slope_factor_groove_top" :onkeypress="keydownEnter" clearable ></vxe-input>
            </template>
        </vxe-column>
        <vxe-column title="备注" field="remarks"  :edit-render="{ autofocus: '.vxe-input--inner' }" >
            <template #edit="{ row }">
                <vxe-input v-model="row.remarks" :onkeypress="keydownEnter" ></vxe-input>
            </template>
        </vxe-column>
        <!-- <vxe-column title="地区" field="area"  :edit-render="{ autofocus: '.vxe-input--inner' }" >
            <template #edit="{ row }">
                <vxe-input v-model="row.area" disabled ></vxe-input>
            </template>
        </vxe-column> -->
        <!-- <vxe-column title="关联地区" field="area" width="23%" :edit-render="{ autofocus: '.el-select' }">
            <template #default="{row}">
                <el-tag class="area-tag" v-if="row.area?.length>0" v-for="area in row.area.split(',')"  disable-transitions >{{ area }}</el-tag>
            </template>
            <template #edit="{row}">
                <el-select  v-model="row.temp_areas" placeholder="地区" clearable multiple filterable :reserve-keyword="false" :teleported="false" collapse-tags collapse-tags-tooltip :max-collapse-tags="4"
                 class="area-series-select" @change="row.area=row.temp_areas?.join(',')" >
                    <el-option v-for="area in allAreas" :label="area" :value="area" />
                </el-select>
                <el-button type="success" text icon="Select" @click.stop="saveRow(row)" ></el-button>
                <el-button type="danger" text icon="CloseBold" @click.stop="attrsTable.clearEdit()"  ></el-button>
            </template>
        </vxe-column> -->
        <vxe-column title="操作" width="160">
            <template #default="{ row }">
                <div v-if="attrsTable.isEditByRow(row)">
                    <vxe-button status="success" icon="vxe-icon-save" @click="saveRow(row)"></vxe-button>
                    <vxe-button status="info" icon="vxe-icon-close" @click="attrsTable.clearEdit()"></vxe-button>
                </div>
                <div v-else>
                    <vxe-button status="primary" icon="vxe-icon-edit" @click="attrsTable.setEditRow(row)"></vxe-button>
                    <vxe-button status="danger" icon="vxe-icon-delete" @click="delRow(row)"></vxe-button>
                </div>
            </template>
        </vxe-column>
    </vxe-table>
</template>

<script>
import { GetBidCalcStepSlopeConfig,SaveData,DeleteData,GetAreas,CopyCalcStepSlopeConfig } from '@/api/bidCalcStepSlopeConfig'
import { ref } from 'vue'
import { VXETable, VxeTable } from 'vxe-table';
import {  allAreas,soilCategories  } from '@/calcRule/calc_config'
export default {
    name: "bidCalcStepSlopeTable",
    setup() {
        const attrsTable = ref<VxeTable>({});
        return { attrsTable ,allAreas,soilCategories }
    },
    data() {
        return {
            tableLoading: true,
            tbData: [],
            currentArea:'北京',
            configAreas:null,
            opArea:null,
            opShow:false
        }
    },
    async mounted() {
        let query_area=this.$route.query.area;
        if(query_area)this.currentArea=query_area;
        await this.LoadData();
        this.configAreas=await GetAreas();
    },
    methods: {
        /**
         * 加载构件类型
         */
        async LoadData() {
            this.tableLoading = true;
            this.tbData = await GetBidCalcStepSlopeConfig(this.currentArea);
            this.tableLoading = false;
        },
        /**
         * enter键退出编辑
         * @param {object} e 
         */
         keydownEnter(e){
            if(e.code==='Enter') this.attrsTable.clearEdit();
        },
        async delRow(row){
            if (this.attrsTable.isInsertByRow(row)) {
                this.attrsTable.remove(row); 
                return;
            }
            let type=await VXETable.modal.confirm("您确定要删除该数据?","删除确认",{zIndex:9998});
            if(type==='confirm'){
                let result= await DeleteData(row.id);
                if(result){
                    VXETable.modal.message({content:'删除成功!',status:'success',zIndex:9999});
                }
                else{
                    VXETable.modal.message({content:'删除失败!',status:'error',zIndex:9999});
                }
                await this.LoadData();
            }
        },
        async newRow(){
            let temp_row=await this.attrsTable.insertAt({id:null,remarks:null,area:this.currentArea});
            await this.attrsTable.setEditRow(temp_row.row);
        },
        async saveRow(row){
            this.tableLoading = true;
            this.attrsTable.clearEdit();
            if (this.attrsTable.isInsertByRow(row)) {
                row.id=null; 
            }
            let result=  await SaveData([row]);
            if(result){
                VXETable.modal.message({content:'保存成功!',status:'success',zIndex:9999});
            }
            else{
                VXETable.modal.message({content:'保存失败!',status:'error',zIndex:9999});
            }
            await this.LoadData();
        },
        formatSoilCategory({row}){
            return this.soilCategories.find(s=>s.value==row.soil_category)?.label;
        },
        showEdit(row){
            // row.temp_areas=row.area?.length>0?row.area?.split(','):null;this.attrsTable.setEditRow(row);
            if(this.attrsTable.isEditByRow(row)){
                this.attrsTable.clearEdit();
            }
            else{
                this.attrsTable.setEditRow(row);
            }
        },
        editClosed({row}){
            if(row.slope_factor_groove_top==='')row.slope_factor_groove_top=null;
            if(row.slope_factor_pit_top==='')row.slope_factor_pit_top=null;
            if(row.slope_factor_pit_inside==='')row.slope_factor_pit_inside=null;
            if(row.slope_factor_artificial==='')row.slope_factor_artificial=null;
            // for(const [key,value] of Object.entries(row)){
            //     if(value==='')row[key]=null;
            // }
        },
        async areaChange(val){
            this.tbData=await GetBidCalcStepSlopeConfig(val);
            await this.attrsTable.reloadData(this.tbData);
        },
        async copyData(){
            let result= await CopyCalcStepSlopeConfig(this.currentArea,this.opArea);
            if(result){
                VXETable.modal.message({content:'保存成功!',status:'success',zIndex:9999});
                this.currentArea=this.opArea;
            }
            else{
                VXETable.modal.message({content:'保存失败!',status:'error',zIndex:9999});
            }
            this.opShow=false;
        }
    }
}
</script>

<style scoped lang="scss">
.bcom-header {
    padding: 10px;
    background-color: var(--el-color-info-light-9);
    height: 45px;
    display: block;
}

.title {
    display: flex;
    justify-content: center;
    align-items: center;
}
:deep(.vxe-buttons--wrapper .el-select){
    margin-right: 12px;
}
.area-tag{
    margin-left: 4px;
    margin-bottom: 2px;
}
.area-series-select{
    width:84%;
    margin-right:5px;
}
:deep(.vxe-body--column .el-button){
    height:24px;
    padding: 5px 2px ;
}
.copy-area-prop .el-select {
    margin-left: 10px;
}

.copy-area-prop .el-button {
    margin-left: 10px;
}

.copy-area-prop .el-select+.el-text {
    margin-left: 10px;
}
.copy-area-prop .el-text+.el-text {
    margin-left: 10px;
}

.col--actived .vxe-cell .el-text{
    margin: 0 7px;
}
</style>