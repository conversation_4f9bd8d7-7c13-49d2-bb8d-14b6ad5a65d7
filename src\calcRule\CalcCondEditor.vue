<template>
    <el-text size="large" class="kh" type="warning" v-if="cond?.or?.length>0 || cond?.child?.length>0">{{ cond?.or?.length>0?'[':'{' }}</el-text>
    <el-text type="warning" v-if="isOrCond"> 或 </el-text>
    <el-text type="warning" v-if="isChildCond"> 且 </el-text>
    <el-tag  disable-transitions type="info" closable class="bg-purple2" @close="delCond" >
        <template #default>

            <!-- <el-text v-if="isChildCond&&pBCom" size="small" class="text-bcom" >{{ calcBComsMap?.get(pBCom) }}</el-text> -->
            <el-text v-if="isChildCond&&pBCom" size="small" class="text-bcom" >{{ pBCom }}</el-text>

            <!-- 属性条件 -->
            <el-select v-if="cond.ctype=='1-2'||cond.ctype=='1-3'" class="ew" size="small" placeholder="属性" v-model="cond.attr" filterable>
                <el-option v-for="attr in calcAttrs" :label="attr.label" :value="attr.value" />
            </el-select>
            <!-- 比较符 -->
            <el-select v-if="cond.ctype=='1-2'" class="ew-tj" size="small" placeholder="比较条件" v-model="cond.op" filterable :reserve-keyword="false" >
                <el-option v-for="item in compateOptions" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
            <!-- 属性值 多个 -->
            <el-select v-if="cond.ctype=='1-2'&& calcAttrs.find(d=>d.value==cond.attr)?.values" class="ew-mselect" size="small" placeholder="属性值" v-model="cond.vals" clearable filterable multiple :multiple-limit="isMultiple(cond.op)?0:1" :collapse-tags="isMultiple(cond.op)"  >
                <el-option v-for="val in calcAttrs.find(d=>d.value==cond.attr).values" :label="val" :value="val"  />
            </el-select>
            <el-select v-else-if="cond.ctype=='1-2'&& isMultiple(cond.op)" class="ew-mselect" size="small" placeholder="属性值" v-model="cond.vals" clearable filterable multiple allow-create collapse-tags >
                <el-option v-for="val in cond.vals" :label="val" :value="val"  />
            </el-select>
            <el-input v-else-if="cond.ctype=='1-2'&& !isMultiple(cond.op)" class="ew-mselect" size="small" placeholder="属性值" v-model="cond.vals[0]"></el-input>

            <!-- 范围 -->
            <el-input v-if="cond.ctype=='1-3'" class="ew" size="small" placeholder="起始" v-model="cond.sval"></el-input>
            <el-text v-if="cond.ctype=='1-3'"> ~ </el-text>
            <el-input v-if="cond.ctype=='1-3'" class="ew" size="small" placeholder="终止" v-model="cond.eval"></el-input>

            <!-- 位置条件 -->
            <el-text type="info" v-if="cond.ctype=='2-1' && (cond.position =='2-7'||cond.position =='2-8'||cond.position =='2-9'|| cond.position=='2-10')"  size="small" >与</el-text>
            <el-text type="info" v-else-if="cond.ctype=='2-1' && cond.position !='2-6'"  size="small" >在</el-text>
            
            <!-- <el-text type="info" v-if="cond.ctype=='2-1'&&isDeductCond" size="small" class="text-bcom" >计算规则构件</el-text> -->
            <!-- <el-select v-if="cond.ctype=='2-1'&&isDeductCond" class="ew-tj" size="small" placeholder="位置条件" v-model="cond.position" filterable :reserve-keyword="false" >
                <el-option-group label="位置判断">
                    <el-option v-for="item in positionOptions" :key="item.value" :label="item.label" :value="item.value" />
                </el-option-group>
            </el-select> -->

            <el-select v-if="cond.ctype=='2-1'&&cond.position=='2-6'" class="ew-tj" size="small" placeholder="位置条件" v-model="cond.position" filterable :reserve-keyword="false" >
                <el-option-group label="位置判断">
                    <el-option v-for="item in positionOptions" :key="item.value" :label="item.label" :value="item.value" />
                </el-option-group>
            </el-select>

            <!-- <el-cascader v-if="cond.ctype=='2-1'" style="width: 340px;" size="small" @change="bcomChange" placeholder="构件选择" ref="bcomCascader" :props="cprops" :options="graphicalComponents" filterable clearable  collapse-tags collapse-tags-tooltip :max-collapse-tags="1"  /> -->

            <el-select v-if="cond.ctype=='2-1'"  size="small" v-model="cond.bcom"  placeholder="构件"  >
                                <el-option key="柱" label="柱" value="柱" />
                                <el-option key="梁" label="梁" value="梁" />
                                <el-option key="板" label="板" value="板" />
                                <el-option key="基础" label="基础" value="基础" />
                            </el-select>

            <el-select v-if="cond.ctype=='2-1'&&cond.position!='2-6'" class="ew-tj" size="small" placeholder="位置条件" v-model="cond.position" filterable :reserve-keyword="false" >
                <el-option-group label="位置判断">
                    <el-option v-for="item in positionOptions" :key="item.value" :label="item.label" :value="item.value" />
                </el-option-group>
            </el-select>
            
            <el-popover v-if="!isOrCond && !isChildCond && !(cond?.child?.length>0)" placement="right" trigger="click" >
                <template #reference>
                    <el-button size="small" text type="warning" icon="Switch" ></el-button>
                </template>
                <el-select size="small" @change="(val)=>addCurrentCond(val,'or')" placeholder="选择条件" >
                    <el-option v-for="item in condOptions" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
            </el-popover>

            <el-popover v-if="!isOrCond && !isChildCond && !(cond?.or?.length>0) && cond.ctype=='2-1'" placement="right" trigger="click" >
                <template #reference>
                    <el-button size="small" text type="warning" icon="Link" ></el-button>
                </template>
                <el-select size="small" @change="(val)=>addCurrentCond(val,'child')" placeholder="选择条件" >
                    <el-option v-for="item in condOptions" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
            </el-popover>

        </template>
    </el-tag>

    <calcCondEditor v-if="cond?.or?.length>0" v-for="(orCond,index) in cond.or" :cond="orCond"  :isOrCond="true" :delCond="()=>delCurrentCond(cond.or,index)" />

    <calcCondEditor v-if="cond?.child?.length>0" v-for="(childCond,index) in cond.child" :cond="childCond" :isChildCond="true" :delCond="()=>delCurrentCond(cond.child,index)" :pBCom="cond.bcom" />

    <el-text size="large" class="kh" type="warning" v-if="cond?.or?.length>0 || cond?.child?.length>0">{{ cond?.or?.length>0?']':'}' }}</el-text>
</template>
<script>
import {condOptions,compateOptions,positionOptions,isMultiple,calcAttrs} from '@/calcRule/calc_config'

export default {
    name: 'calcCondEditor',
    inject: [ 'graphicalComponents'],
    props: {
        /**
         * 删除func
         */
        delCond:Function,
        /**
         * 条件对象
         */
        cond: Object,
        /**
         * 或条件
         */
        isOrCond:Boolean,
        /**
         * 子条件
         */
        isChildCond:Boolean,
        /**
         * 扣减条件
         */
        isDeductCond:Boolean,
        /**
         * 父节点构件
         */
        pBCom:String
    },
    mounted(){
        // this.attributesLoad();
    },
    setup(){
        const cprops = {  multiple: true,  label:'title',value:'id'};
        return {condOptions,compateOptions,positionOptions,isMultiple,calcAttrs,cprops}
    },
    data(){
        return {
            atLoading: false,
            localAttributes: [],
        }
    },
    methods:{
        // attributesLoad(keyword) {
        //     this.atLoading = true;
        //     if (keyword) {
        //         this.localAttributes = this.bidAttributes.filter(c => c.name.toLowerCase().includes(keyword.toLowerCase())).sort(this.lengtSort);
        //     }
        //     else {
        //         this.localAttributes =  this.bidAttributes;// this.cond.attr?this.bidAttributes.filter(c=>c.id==this.cond.attr):[]; 
        //     }
        //     this.atLoading = false;
        // },
        /**
         * 添加条件
         * @param {String} op 条件类型 
         */
         addCurrentCond(op,type) {
            let tempCond = null;
            switch (op) {
                case '1-1':
                    tempCond = { ctype: op, attr: null, op: null, tbcom: null, tattr: null };
                    break;
                case '1-4':
                    tempCond = { ctype: op, attr: null, op: null, tbcom: null, tattr: null, factor: 1 };
                    break;
                case '1-2':
                    tempCond = { ctype: op, attr: null, op: null, vals: [] };
                    break;
                case '1-3':
                    tempCond = { ctype: op, attr: null, op: null, sval: null, eval: null };
                    break;
                case '2-1':
                    tempCond = { ctype: op, bcom: null, position: null };
                    break;
                default:
                    break;
            }
            if(!this.cond[type])this.cond[type]=[];
            if(tempCond)this.cond[type].push(tempCond);            
        },
        /**
         * 删除条件
         */
         delCurrentCond(conds,cIndex) {
            if(cIndex>=0)conds.splice(cIndex,1);
        },
        /**
         * 构件变更
         * @param {Array} val 二维数组
         */
        bcomChange(val){
            // this.cond.bcom=val[1];
            let checked_nodes= this.$refs.bcomCascader.getCheckedNodes();
            this.cond.bcom= checked_nodes?.filter(n=>n.level==2)?.map(n=>n.text)?.join(',');
        }
        
    },
}
</script>

<style scoped>
.ew-tj{
    width: 96px !important;
}
.ew {
    width: 142px !important;
}
.ew-mselect {
    width: 172px !important;
}
.gj-select{
    width: 260px !important;
}
.bg-purple2 {
    padding: 0 3px 0 3px;
    display: flex;
    align-items: center;
    align-self: center;
    margin: 3px;
    min-height: 32px;
}

:deep(.el-tag__content){
    display: flex;
    align-items: center;
}

:deep(.el-tag__content .el-text+.el-select){
    margin-left: 8px;
}

.kh{
    margin: 0 3px 3px 3px;
    font-size: 22px;
}
.text-bcom{
    margin-left: 8px;
}

:deep(.el-button+.el-button){
    margin-left: 0;
}
:deep(.el-button--small){
    padding: 5px 5px;
}
</style>
