<template>
        <el-form label-width="100px" >
            <el-form-item label="分类" >
                <el-select v-model="currentRow.classification_id" persistent :teleported="false" placeholder="分类"  >
                    <el-option v-for="item in classifications" :key="item.id" :value="item.id" :label="item.name" ></el-option>
                </el-select>
            </el-form-item>
            <el-form-item label="计算规则构件">
                <el-input v-model="currentRow.name" style="width: 600px;"></el-input>
            </el-form-item>
            <el-form-item label="别名">
                <el-input v-model="currentRow.alias" style="width: 600px;"></el-input>
            </el-form-item>

            <el-form-item label="条件" class="cond-tr" >
                <bcomSortCondEditor v-for="(acond, aindex)  in currentRow.conds" :cond="acond" :delCond="()=>delCond(currentRow.conds,aindex)"  />
                <el-popover placement="right" trigger="click" >
                    <template #reference>
                        <el-button size="small" icon="plus" circle type="primary" class="btn-plus"></el-button>
                    </template>
                    <el-select size="small" @change="(op)=>addCond(currentRow,'conds',op)" placeholder="选择条件" >
                        <el-option v-for="item in condOptions" :key="item.value" :label="item.label" :value="item.value" />
                    </el-select>
                </el-popover>
            </el-form-item>

            <el-form-item label="描述">
                <el-input v-model="currentRow.remarks" style="width: 600px;"></el-input>
            </el-form-item>

            <el-form-item>
                <el-button type="primary" @click="saveData(currentRow)">{{ this.currentRow.id ? '保存' : '新增' }}</el-button>
                <el-button @click="editCancel">取消</el-button>
            </el-form-item>
        </el-form>
</template>
<script>
import bcomSortCondEditor from './BComSortCondEditor.vue'
import {condOptions,ruleTypes,addCond,delCond} from '@/calcRule/calc_config'

export default {
    name: 'BComEditor',
    inject:['bidAttributes','bidAttributesMap','bcomOptions','classifications'],
    components: { bcomSortCondEditor },
    props: {
        saveData: Function,
        editCancel: Function,
        currentRow:Object
    },
    setup() {
        return { condOptions,ruleTypes,addCond,delCond }
    },
    data() {
        return {
            /**
             * 属性
             */
            localAttributes:null,
            /**
             * 属性加载中
             */
            atLoading:false,
        }
    },
    methods: {
        /**
         * 加载属性
         */
        attributesLoad(keyword) {
            this.atLoading = true;
            if (keyword) {
                this.localAttributes = this.bidAttributes.filter(c => c.name.toLowerCase().includes(keyword.toLowerCase())).sort(this.lengtSort);
            }
            else {
                this.localAttributes =  this.bidAttributes;// this.cond.attr?this.bidAttributes.filter(c=>c.id==this.cond.attr):[]; 
            }
            this.atLoading = false;
        },
    },
}
</script>
<style scoped>
.bcom-tabs{
    width: 100%;
}
.btn-plus{
    margin-left: 5px;
}
.bcom-tabs .el-form-item{
    margin-bottom: 10px;
}
.deductText{
    margin-left: 20px;
    margin-right: 10px;
}

.bcom-tabs .i-rank{
    width: 1em;
    height: 1em;
    margin-right: 3px;
    cursor:grab;
}

:deep(.bcom-tabs .el-tabs__new-tab){
    margin-right:12px;
}
:deep(.bcoms-cascader){
    width: 300px;
}
.cond-tr{
    padding:4px;
    border:1px dashed var(--el-border-color);
    border-radius: 4px;
}
</style>

