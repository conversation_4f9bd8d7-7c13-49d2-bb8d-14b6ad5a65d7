<template>
    <vxe-toolbar>
        <template #buttons>
            <vxe-button icon="vxe-icon-square-plus" @click="showEditor({id:null,name:null,alias:null,classification_id:null,remarks:null,conds:[]})">新增</vxe-button>
        </template>
    </vxe-toolbar>
    <vxe-table ref="attrsTable" height="500"  keep-source 
        
        :row-config="{ useKey: true, isHover: true,  keyField: 'id', transform: true ,height:'auto'  }" :loading="tableLoading" :data="tbData"
        @cell-dblclick="({row})=>showEditor(row)">
        <vxe-column title="#" width="60" type="seq" />
        <vxe-column title="计算规则构件"  width="300" field="name" align="center" ></vxe-column>
        <vxe-column title="别名" width="300" field="alias" align="center"></vxe-column>
        <vxe-column title="分类" width="120" align="center">
            <template #default="{row}">{{ formatClassification(row.classification_id) }}</template>
        </vxe-column>1
        <vxe-column title="条件">
            <template #default="{ row }">
                <div class="deduct" v-if="row.conds?.length>0">
                    <el-text type="primary">{{ row.name}} </el-text>
                    <el-text v-if="row.conds?.length>0" type="info">{</el-text>
                    <bcomSortCond v-for="(acond,index) in row.conds" :cond="acond" :hasNext="row?.conds?.length>1 && index<(row?.conds?.length-1)"  />
                    <el-text v-if="row.conds?.length>0" type="info">}</el-text>
                </div>
            </template>
        </vxe-column>
        <vxe-column title="备注" field="remarks" align="center" ></vxe-column>
        <vxe-column title="操作" width="140">
            <template #default="{row}">
                <vxe-button icon="vxe-icon-edit" @click="showEditor(row)"></vxe-button>
                <vxe-button icon="vxe-icon-delete" @click="delRow(row)"></vxe-button>
            </template>
        </vxe-column>
    </vxe-table>
    <el-dialog  v-model="rowEditerShow" title="计算规则构件" width="50%" center align-center append-to-body >
        <BComEditor ref="roweditor" :saveData="saveBCom" :editCancel="() => rowEditerShow = false" :currentRow="currentRow" />
    </el-dialog>
</template>

<script>
import { GetBidCalcBCom,SaveData,DeleteData } from '@/api/bidCalcBCom'
import { ref } from 'vue'
import { VXETable, VxeTable } from 'vxe-table';
import BComEditor from '@/calcRule/BComEditor.vue'
import { cloneDeep } from 'lodash-es'
import bcomSortCond from './BComSortCond.vue'

export default {
    inject:['classifications'],
    name: "bidCalcBComTable",
    components:{BComEditor,bcomSortCond},
    setup() {
        const attrsTable = ref<VxeTable>({});
        return { attrsTable  }
    },
    data() {
        return {
            tableLoading: true,
            tbData: [],
            rowEditerShow:false,
            currentRow:null
        }
    },
    async mounted() {
        await this.LoadData();
    },
    methods: {
        /**
         * 加载构件类型
         */
        async LoadData() {
            this.tableLoading = true;
            this.tbData = await GetBidCalcBCom();
            this.tableLoading = false;
        },
        /**
         * enter键退出编辑
         * @param {object} e 
         */
        async delRow(row){
            if (this.attrsTable.isInsertByRow(row)) {
                this.attrsTable.remove(row); 
                return;
            }
            let type=await VXETable.modal.confirm("您确定要删除该数据?","删除确认",{zIndex:9998});
            if(type==='confirm'){
                let result= await DeleteData(row.id);
                if(result){
                   
                    VXETable.modal.message({content:'删除成功!',status:'success',zIndex:9999});
                }
                else{
                    VXETable.modal.message({content:'删除失败!',status:'error',zIndex:9999});
                }
                await this.LoadData();
            }
        },
        formatClassification(id){
            return this.classifications.find(d=>d.id==id)?.name??id;
        },
        async saveBCom(data){
            let postData={id:data.id,name:data.name,remarks:data.remarks,alias:data.alias,classification_id:data.classification_id,conds:JSON.stringify(data.conds)};
            await SaveData([postData]);
            VXETable.modal.message({content:'保存成功!',status:'success',zIndex:9999});
            await this.LoadData();
            this.rowEditerShow=false;
        },
        /**
         * 显示行编辑弹框
         * @param {Object} row 行数据
         */
         showEditor(row) {
            this.rowEditerShow = true;
            // this.$refs.roweditor.changeData(cloneDeep(row));
            this.currentRow=cloneDeep(row);
        },
        formatBCom(id){
            if(id)return this.tbData.find(d=>d.id==id)?.name;
        }
    }

}
</script>

<style scoped lang="scss">
.dropdown {
    border-radius: 4px;
    border: 1px solid #dcdfe6;
    background-color: #fff;
}

.list-item {
    line-height: 22px;
}

.list-item:hover {
    background-color: #f5f7fa;
}
.value-tag{
    margin-top:3px ;
    margin-bottom: 3px;
}
.value-tag +.value-tag{
    margin-left: 4px;
}
.value-tag +.el-input{
    margin-left: 4px;
}
.value-tag +.el-button{
    margin-left: 4px;
}
.el-text +.el-text{
    margin-left: 4px;
}
</style>