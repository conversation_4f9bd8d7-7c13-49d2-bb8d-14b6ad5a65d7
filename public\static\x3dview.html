<html>
    <head>
        <script src="./x3dom.js" type="text/javascript"></script>
        <link rel="stylesheet" type="text/css" href="./x3dom.css">
    </head>
    <script lang="javascript">
        function changeScene(x3ds){
            let shapeStr='<transform>';
            let scene= document.getElementById('x3dscene');
            for (let index = 0; index < x3ds?.length; index++) {
                const element = x3ds[index];
                shapeStr+=readXML(element.color,element.x3dStr,element.transparency);
            }
            shapeStr+='</transform>';
            scene.innerHTML=shapeStr;
        }
        function readXML(color,xmlStr,transparency=null){
            let xmlResult='';
            let parser=new DOMParser();
            var serializer=new XMLSerializer();
            let xmlDoc=parser.parseFromString(`<root>${xmlStr}</root>`,'text/xml');
            let shapes=xmlDoc.getElementsByTagName('Shape');
            for (let index = 0; index < shapes.length; index++) {
                const shape = shapes[index];
                let mterialNode=xmlDoc.createElement('Material');
                mterialNode.setAttribute('emissiveColor',color);
                if(transparency)mterialNode.setAttribute('transparency',transparency);
                let appearanceNode=xmlDoc.createElement('Appearance');
                appearanceNode.appendChild(mterialNode);
                shape.appendChild(appearanceNode);
                xmlResult+=serializer.serializeToString(shape);
           }
           return xmlResult;
        }
    </script>
    
    <body>
        <x3d id="x3d" style="width:100%;height:100%">
            <scene id="x3dscene">
            </scene>
        </x3d>
    </body>
</html>


