<template>
    <el-form label-width="40px">
        <el-form-item label="地区">
                <el-select size="small" placeholder="选择" style="width: 160px;" v-model="copyCity" filterable  :reserve-keyword="false" >
                    <el-option v-for="city in copyCities" :key="city"  :label="city" :value="city"></el-option>
                </el-select>
            </el-form-item>
        <el-form-item>
            <el-button type="primary" size="small" @click="editSave(copyCity)">确定</el-button>
            <el-button size="small" @click="editCancel">取消</el-button>
        </el-form-item>
    </el-form>
</template>

<script>
import { GetAreasByCGId} from '@/api/rules'

export default {
    inject:['currentArea','selectBcomId'],
    name: 'copyAreas',
    props: {
        editSave: Function,
        editCancel: Function
    },
    async mounted(){
        if(this.selectBcomId)this.copyCities= (await Get<PERSON>reasByCGId(this.selectBcomId)).filter(d=>d!==this.currentArea);
    },
    data() {
        return {
            copyCity:null,
            copyCities: [],
        }
    },
}
</script>