<template>
<div class="deduct" v-if="cond.cond_type==0">
    <el-text v-if="cond.attr_conds?.length > 0" type="info">{</el-text>
    <bcomSortCond v-for="acond in cond.attr_conds" :cond="acond" />
    <el-text v-if="cond.attr_conds?.length > 0" type="info">}</el-text>
    <el-text type="info">属性: </el-text>
    <el-text type="primary">{{ currentAttr}}</el-text>
</div>
<div class="deduct" v-if="cond.cond_type==5">
    <el-text v-if="cond.attr_conds?.length > 0" type="info">{</el-text>
    <bcomSortCond v-for="acond in cond.attr_conds" :cond="acond" />
    <el-text v-if="cond.attr_conds?.length > 0" type="info">}</el-text>
    <el-text type="primary">{{ currentAttr}}</el-text>
    <el-text type="info">计算公式 : </el-text>
    <el-text type="success">{{ currentObj?.name }} : {{ currentObj?.mathml }}</el-text>
</div>
<el-divider direction="vertical" v-if="hasNext" />
</template>
<script>
import bcomSortCond from '@/calcRule/BComSortCond.vue'

export default {
    inject:['bidAttributesMap','allFormulas'],
    name: 'bComCalcFormula',
    components:{bcomSortCond},
    props: {
        cond: Object,
        hasNext:Boolean
    },
    mounted(){
        switch (this.cond.cond_type) {
            //加载公式内容
            case 5:
                this.currentObj=this.allFormulas.find(d=>d.id==this.cond.formula);
                break;
            default:
                break;
        }
    },
    data(){
        return {
            currentObj:{},
            /**
             * 属性
             */
            currentAttr: this.bidAttributesMap.get(this.cond.attr)
        }
    }
}
</script>