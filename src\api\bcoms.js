import axios from "axios"

/**
 * 获取所有父级节点
 * @param {string} id 
 * @returns 
 */
export function GetRootIds(id) {
    return axios.get(`/ComGroup/QueryOptionRootIds?id=${id}`)
    .then((res) => {
        return res.data.data;
    })
}

/**
 * 获取多个构件(组)信息
 * @param {Array} ids 
 * @returns 
 */
export function GetBcomInfos(ids){
    return  axios.post(`/ComGroup/GetByIds`,ids)
        .then((res) => {
            return res.data.data;
        })
}

/**
 * 获取构件(组)
 * @param {String} id 节点id
 * @param {Number} queryType 过滤类型(小于) 1 大专业 2 中专业  3小专业 4 构件 5 子构件
 * @returns 
 */
export function  GetBComChilds(id,queryType,area){
    return  axios.get(`/ComGroup/QueryChildOptions?id=${id}&queryType=${queryType}&area=${area??''}`)
        .then((res) => {
            return res.data.data;
        })
}

/**
 * 获取构件(组)全称
 * @param {String} id 节点id 
 * @param {Boolean} includingSelf 是否包含自身 
 * @returns 
 */
export function GetFullName(id,includingSelf=false){
  return  axios.get(`/ComGroup/QueryOptionFullName?id=${id}&includingSelf=${includingSelf}`)
    .then((res) => {
        return res.data.data.join(' / ');
    })
}

/**
 * 获取所有构件(专业)
 * @param {String} pid 
 * @param {Number} type 查询类型 1 构件  2小专业 3中专业
 * @param {String} area 地区
 * @returns 
 */
export function GetAllBComChilds(pid, type,area) {
    return  axios.get(`/ComGroup/QueryAllChildsAsOption?pid=${pid}&queryType=${type}&area=${area??''}`)
        .then((res) => {
            return res.data.data;
        })
}

/**
 * 获取所有构件(统称)
 * @param {String} pid 
 * @param {Number} type 查询类型 1 构件  2小专业 3中专业
 * @param {String} area 地区
 * @returns 
 */
 export function GetAllChilds(pid,area) {
    return  axios.get(`/ComGroup/QueryAllChilds?pid=${pid}&area=${area??''}`)
        .then((res) => {
            return res.data.data;
        })
}

/**
 * 获取地区所有构件id
 * @param {String} area 地区
 * @param {Boolean} refreshCache 刷新缓存
 * @returns 
 */
 export function GetAreaChilds(area,refreshCache) {
    return  axios.get(`/ComGroup/QueryAreaChilds?area=${area??''}&refresh=${refreshCache??false}`)
        .then((res) => {
            return res.data.data;
        })
}

/**
 * 获取所有构件和统称
 * @param {String} pid 
 * @returns 
 */
export function GetAllChildsWhitThiunify(pid) {
    return  axios.get(`/ComGroup/QueryAllChildsWhitThiunify?pid=${pid??'-1'}`)
        .then((res) => {
            return res.data.data;
        })
}

