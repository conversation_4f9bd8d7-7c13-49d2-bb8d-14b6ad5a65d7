<template>
    <div class="deduct" v-if="cond.cond_type==1">
        <el-text v-if="cond.attr_conds?.length > 0" type="info">{</el-text>
        <bcomSortCond v-for="acond in cond.attr_conds" :cond="acond" />
        <el-text v-if="cond.attr_conds?.length > 0" type="info">}</el-text>
        <el-text type="primary">{{ currentAttr }}</el-text>
        <el-text type="info">计算系数 : </el-text>
        <el-text type="success">{{ cond.factor }}</el-text>
    </div>

    <div class="deduct" v-if="cond.cond_type==2">
        <el-text v-if="cond.attr_conds?.length > 0" type="info">{</el-text>
        <bcomSortCond v-for="acond in cond.attr_conds" :cond="acond" />
        <el-text v-if="cond.attr_conds?.length > 0" type="info">}</el-text>
        <el-text type="info">属性转换 </el-text>
        <el-tag v-if="cond.attr" effect="light" disable-transitions>
            <el-text type="info">{{ `${currentAttr}  = ${bidAttributesMap.get(cond.trans_attr)} * ` }}</el-text><el-text type="success">{{ cond.factor }}</el-text>
        </el-tag>
    </div>

    <div class="deduct" v-if="cond.cond_type==5">
        <el-text v-if="cond.attr_conds?.length > 0" type="info">{</el-text>
        <bcomSortCond v-for="acond in cond.attr_conds" :cond="acond" />
        <el-text v-if="cond.attr_conds?.length > 0" type="info">}</el-text>
        <el-text type="primary">{{ currentAttr}}</el-text>
        <el-text type="info">计算公式 : </el-text>
        <el-text type="success">{{ currentObj?.name }} : {{ currentObj?.mathml }}</el-text>
    </div>
    <el-divider direction="vertical" v-if="hasNext" />
</template>
<script>
import bcomSortCond from '@/calcRule/BComSortCond.vue'

export default {
    inject:['bidAttributesMap','thicknessTables','allFormulas'],
    name: 'bcomCalcRule',
    components:{bcomSortCond},
    props: {
        cond: Object,
        hasNext:Boolean
    },
    mounted(){
        switch (this.cond.cond_type) {
            case 4:
                this.currentObj=this.thicknessTables.find(d=>d.value==this.cond.thicknessTable);
                break;
            case 5:
                this.currentObj=this.allFormulas.find(d=>d.id==this.cond.formula);
                break;
            default:
                break;
        }
    },
    data(){
        return {
            currentObj:{},
            currentAttr: this.bidAttributesMap.get(this.cond.attr)
        }
    }
}
</script>