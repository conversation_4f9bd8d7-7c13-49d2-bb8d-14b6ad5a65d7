import axios from "axios"

/**
 * 获取二次生成配置
 * @returns 
 */
export function GetCalcSGConfig(key,area){
    return axios.get(`/BidCalcBCom/GetCalcSGConfig?key=${key}&area=${area}`)
        .then((res) => {
            let temp_data=res.data.data;
            if(temp_data?.length>0){
                temp_data.forEach(d => d.conds=JSON.parse(d.conds));
            }
            return temp_data;
            // return res.data.data;
        })
}

/**
 * 保存
 * @param {Object} data 
 * @returns 
 */
export function SaveData(data){
    return axios.post(`/BidCalcBCom/SaveCalcSGConfig`, data)
    .then((res) => {
        return  res?.data?.succeeded;
    })
}

/**
 * 删除
 * @param {Number} id
 * @returns 
 */
export function DeleteData(id){
    return axios.post(`/BidCalcBCom/DelCalcSGConfig`, {id:id})
    .then((res) => {
        return res?.data?.succeeded;
    })
}

/**
 * 获取已配置地区
 * @returns 
 */
 export function GetAreas(sgKey){
    return axios.get(`/BidCalcBCom/GetCalcSGConfigAreas?sgKey=${sgKey}`)
        .then((res) => {
            return res.data.data;
        })
}

/**
 * 更新排序
 * @param {Array} ids
 * @returns 
 */
 export function UpdateCalcSGSerial(ids){
    return axios.post(`/BidCalcBCom/UpdateCalcSGSerial`, {items:ids})
    .then((res) => {
        return res?.data?.succeeded;
    })
}

