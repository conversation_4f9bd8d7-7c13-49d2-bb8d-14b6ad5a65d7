<template>
    <el-row style="height:100%">
        <el-col :span="24">
            <el-affix :offset="0">
                <el-row class="bcom-header">
                    <el-row class="title">
                        <el-text size="large" tag="b">构件扣减规则</el-text>
                    </el-row>
                    <el-row class="link">
                        <el-link target="_blank" icon="Notification" href="/bidComCbalcRule">构件计算规则</el-link>
                        <el-link target="_blank" icon="Collection" :href="'/sgconfig?area='+this.currentArea">二次生成配置</el-link>
                        <el-link target="_blank" icon="House" :href="'/stepslopeconfig?area='+this.currentArea">放坡系数配置</el-link>
                        <el-link target="_blank" icon="ScaleToOriginal" href="/bidComCalcFormula">构件计算公式配置</el-link>
                    </el-row>
                </el-row>
                <el-row class="bcom-choose">
                    <el-col :span="16">
                        <el-text style="width: 50px;">地区</el-text>
                        <el-radio-group v-model="currentArea" @change="areaChange">
                            <el-radio v-for="area in allAreas" :label="area" border :key="area">{{ area }}</el-radio>
                        </el-radio-group>
                    </el-col>
                    <el-col :span="8">
                        <div class="tabs-buttons">
                            <el-button text icon="Notification" type="primary" @click="showOp($event.target, 1)">复制地区</el-button>
                            <el-button text icon="CopyDocument" type="primary" @click="showOp($event.target, 3)" >复制数据</el-button>
                            <el-popover width="760px" title="复制" 
                                @show="loadAreaCounts" :visible="opShow" :virtual-ref="opRef" virtual-triggering >
                                <div v-if="opType<3">
                                    <el-row>
                                        <el-col>
                                            <el-form-item label="定额系列" label-width="80px">
                                                <el-select v-model="opSeries" placeholder="系列" filterable :reserve-keyword="false" style="width: 400px;">
                                                    <el-option v-for="val in opAreaSeries" :label="`${val.series + (areaCounts?.find(a => a.val == (area+val.id)) ? `(${areaCounts?.find(a => a.val == (area+val.id))?.count})` : '')}`" :value="val.id" :disabled="val.id == currentSeries" :key="val.id" />
                                                </el-select>
                                            </el-form-item>
                                        </el-col>
                                    </el-row>
                                    <el-row>
                                        <el-col>
                                            <el-form-item label="构件" label-width="80px">
                                                <el-cascader v-model="opClassification" placeholder="构件选择" :options="bcomOptions" :props="cprops"
                                                    filterable clearable collapse-tags collapse-tags-tooltip :teleported="false" style="width: 400px;" />
                                            </el-form-item>
                                        </el-col>
                                    </el-row>
                                    <el-row>
                                        <el-col>
                                            <el-form-item label-width="80px">
                                                <el-button type="primary" @click="copyData" :disabled="!opSeries">确定</el-button>
                                                <el-button type="primary" @click="opShow = false">取消</el-button>
                                            </el-form-item>
                                        </el-col>
                                    </el-row>
                                </div>
                                <div v-if="opType==3">
                                    <el-form :inline="true">
                                        <el-form-item label="从">
                                            <el-cascader v-model="copySourceClassification" placeholder="构件选择" :options="bcomOptions" 
                                                filterable clearable collapse-tags collapse-tags-tooltip :teleported="false" style="width: 270px;" />
                                        </el-form-item>
                                        <el-form-item label="复制到">
                                            <el-cascader v-model="opClassification" placeholder="构件选择" :options="bcomOptions" 
                                                filterable clearable collapse-tags collapse-tags-tooltip :teleported="false" style="width: 270px;" />
                                        </el-form-item>
                                    </el-form>
                                    <el-form :inline="true">
                                        <el-form-item>
                                            <el-button type="primary" @click="copyRowData" :disabled="!opClassification||!copySourceClassification">确定</el-button>
                                            <el-button type="primary" @click="opShow = false">取消</el-button>
                                        </el-form-item>
                                    </el-form>
                                </div>
                            </el-popover>

                            <el-divider direction="vertical" />
                            <el-button plain icon="Top" @click="changeSerial(true)" :disabled="!canSortToTop"></el-button>
                            <el-button plain icon="Bottom" @click="changeSerial(false)" :disabled="!canSortToBottom"></el-button>
                            <el-divider direction="vertical" />
                            <el-button text icon="SetUp" @click="bComSeriesTableShow = !bComSeriesTableShow">定额系列</el-button>
                            <el-button text icon="Memo" @click="bComAttributeTableShow = !bComAttributeTableShow">构件属性</el-button>
                            <el-button type="primary" @click="showEditor($event.target, {bcom: null, attr_conds: [], target_bcom:null, target_attr_conds: [],classification: null, serial: this.currentRowSerial, remarks: null})">添加构件</el-button>
                        </div>
                        <el-dialog v-if="bComAttributeTableShow" v-model="bComAttributeTableShow" title="构件属性" width="70%" center align-center destory-on-close @closed="loadBComAttributes">
                            <bComCalcAttributeTable  />
                        </el-dialog>

                        <el-dialog v-if="bComSeriesTableShow" v-model="bComSeriesTableShow" title="定额系列" width="60%" center align-center destory-on-close @closed="loadSeries">
                            <bComSeries />
                        </el-dialog>

                        <el-dialog v-if="bComCalcClassificationTableShow" v-model="bComCalcClassificationTableShow" title="构件分类" width="50%" center align-center destory-on-close @closed="()=>{loadClassification();loadSortData();}">
                            <bComCalcClassificationTable  />
                        </el-dialog>
                        <el-dialog v-if="bidCalcBComTableShow" v-model="bidCalcBComTableShow" title="计算规则构件" width="70%" center align-center destory-on-close @closed="()=>{loadCalcBCom();}">
                            <bidCalcBComTable  />
                        </el-dialog>

                    </el-col>
                </el-row>
                <el-row class="bcom-choose">
                    <el-col :span="6">
                        <el-text>系列</el-text>
                        <el-select v-model="currentSeries" placeholder="系列" filterable :reserve-keyword="false" allow-create @change="currentSeriesChange" style="width: 520px;">
                            <el-option v-for="item in areaSeries" :label="item.series ?? '未命名(空)'" :value="item.id" :key="item.id" />
                        </el-select>
                    </el-col>
                    <el-col :span="6">
                        <el-text>构件分类</el-text>
                        <el-cascader v-model="currentClassification" :props="cprops" placeholder="构件选择" :options="bcomOptions" 
                        filterable clearable collapse-tags collapse-tags-tooltip  @change="loadSortData" :teleported="false" />
                        <el-checkbox style="margin-left: 10px;" v-model="showCurrentArea" @change="switchCurrenArea" >只显示当前地区</el-checkbox>
                    </el-col>
                    <el-col :span="2">
                        <el-text>锁定地区</el-text>
                        <el-switch v-model="areaLock" :loading="lockLoding" :before-change="beforeLockChange" size="large" active-icon="Unlock" inactive-icon="Lock" style="--el-switch-on-color:#13ce66;--el-switch-off-color:#ff4949" ></el-switch>
                    </el-col>
                    <el-col :span="6">
                        <el-button text icon="Setting" @click="bComCalcClassificationTableShow = !bComCalcClassificationTableShow">构件分类</el-button>
                        <el-button text icon="Files" @click="bidCalcBComTableShow = !bidCalcBComTableShow">计算规则构件</el-button>
                    </el-col>
                </el-row>
            </el-affix>
            <el-row>
                <el-col>
                    <vxe-toolbar ref="toolbarRef" custom></vxe-toolbar>
                    <vxe-table id="deductTable" ref="dTable" :data="tableData" class="sort-table" show-overflow stripe keep-source
                        :tree-config="{expandAll: false,transform:true,expandRowKeys:treeExpandKeys}"
                        :loading="sortDataLoading" border height="840" :column-config="{ resizable: true }"
                        @cell-dblclick="cellDBClick"
                        :edit-config="{ enabled:areaLock, trigger: 'dblclick', showStatus: false, mode: 'cell',beforeEditMethod:({row})=>row.temp_series=row.all_series }" 
                        :tooltip-config="{contentMethod:showAreaTooltip}"
                        :row-config="{ useKey: true, isHover: true, isCurrent: true, keyField: 'id', transform: true, height: '28' }"
                        @current-change="currentChange" :span-method="mergeRowColumnMethod"
                        :checkbox-config="{checkField:'connected',showHeader:false,checkMethod:()=>this.areaLock,checkStrictly:true}"
                        @checkbox-change="checkBoxChange"
                        @toggle-tree-expand="treeExpand"
                        :row-class-name="mergeRowClass"
                        :custom-config="{storage:true}"
                        >
                        <!-- :scroll-y="{enabled:true,gt:0}" -->
                        <vxe-column title="计算规则构件" field="bcom" width="192" align="center" :formatter="formatName" tree-node />

                        <vxe-column title="目标计算规则构件" field="target_bcom" width="192" align="center" :formatter="formatTargetBCom" />

                        <vxe-column title="扣减规则">
                            <template #default="{ row }">
                                <bcomDecuctCond v-for="(setting,index) in row.deduct_settings" :row="row" :setting="setting" :key="setting"
                                 :hasNext="row?.deduct_settings?.length>1 && index<(row?.deduct_settings?.length-1)"  />
                            </template>
                        </vxe-column>
                        <vxe-column type="checkbox" :title="`关联到${currentArea}`" width="110" class-name="checkbox-cell"></vxe-column>
                        <vxe-column title="当前地区" width="110" field="connected">
                            <template #default="{row}">
                                <el-tag class="area-tag" v-if="!row.level && row.connected" disable-transitions >{{ allSeries.find(s=>s.id==currentSeries).label }}</el-tag>
                            </template>
                        </vxe-column>
                        <vxe-column title="关联地区" field="all_series" width="18%" :edit-render="{ autofocus: '.el-select' }">
                            <template #default="{row}">
                                <el-tag disable-transitions type="info" v-if="getSeriesByCurrentType(row.all_series)?.length>0"  >({{ getSeriesByCurrentType(row.all_series)?.length }})</el-tag>
                                <el-tag class="area-tag" v-for="val in getSeriesByCurrentType(row.all_series)" disable-transitions :key="val" >{{ allSeries.find(s=>s.id==val).label }}</el-tag>
                            </template>
                            <template #edit="{row}">
                                <el-select size="small" v-model="row.temp_series" placeholder="地区" multiple filterable :reserve-keyword="false" :teleported="false" collapse-tags collapse-tags-tooltip :max-collapse-tags="2" class="area-series-select" >
                                    <el-option-group :label="currentSeriesType==1?'定额':'清单'">
                                        <el-option v-for="s in allSeries.filter(s=>s.type==currentSeriesType)" :label="s.label" :value="s.id" :disabled="!areaLock && s.area==currentArea" :key="s.id" />
                                    </el-option-group>
                                    <el-option-group :label="currentSeriesType==1?'定额':'清单'" disabled>
                                        <el-option v-for="s in allSeries.filter(s=>s.type!=currentSeriesType)" :label="s.label" :value="s.id" :disabled="!areaLock && s.area==currentArea" :key="s.id" />
                                    </el-option-group>
                                </el-select>
                                <el-button type="success" text icon="Select" @click.stop="saveRowRelationArea(row)"></el-button>
                                <el-button type="danger" text icon="CloseBold" @click.stop="dTable.clearEdit()"></el-button>
                            </template>
                        </vxe-column>
                        <vxe-column field="remarks" title="描述" width="200"></vxe-column>
                        <vxe-column title="操作" width="202">
                            <template #default="{ row }">
                                <el-button type="success" text icon="Plus" v-if="row.level!=1"
                                    @click.stop="showEditor($event.target,{ bcom: row.bcom, attr_conds: row.attr_conds,classification:row.classification,
                                    target_bcom: row.target_bcom, target_attr_conds: row.target_attr_conds, serial: row.serial,remarks: null,deduct_settings:row.deduct_settings })"></el-button>
                                <el-button type="primary" text icon="Edit" v-if="!row.level" @click.stop="showEditor($event.target, row)" :ref="`edit-${row.id}`"></el-button>
                                <el-button type="warning" text icon="Connection" v-if="!row.level" @click.stop="copyConnRow(row)" :disabled="!(row.target_bcom?.length>0)" ></el-button>
                                <el-button type="danger" text icon="Close" v-if="!row.level" @click.stop="DeleteData(row)"></el-button>
                            </template>
                        </vxe-column>
                    </vxe-table>
                </el-col>
            </el-row>
        </el-col>
    </el-row>
    <el-popover width="1310px" title="扣减规则" :visible="rowEditerShow" placement="left" :virtual-ref="editRef" virtual-triggering>
        <bComCalcRelationEditorNew ref="relationEditor" :saveData="saveData" :editCancel="() => rowEditerShow = false" />
    </el-popover>
    
</template>

<script>
import { ref, computed } from 'vue'
import { cloneDeep } from 'lodash-es'
import { VxeTable,VxeToolbar } from 'vxe-table';
import { GetBComAttributes } from '@/api/bComCalcAttribute'
import { UpdateSerial, QueryByClassifications,GetAllSeries, SaveAsync, DeleteAsync, ExistsAsync, QueryAreaCount, 
    TransferData,CopyRowData,DeleteBidCalcRelationAreasBySeries,SaveBidCalcRelationAreasBySeries,SaveAllBidCalcRelationArea,SaveBidCalcRelationAreas,GetLocked,ChangeLock,LockCheck } from '@/api/bidCalcRelation'
import bcomSortCondEditor from '@/calcRule/BComSortCondEditor.vue'
import bcomSortCond from '@/calcRule/BComSortCond.vue'
import { ElMessage, ElMessageBox } from 'element-plus';
import bComCalcAttributeTable from '@/calcRule/BComCalcAttributeTable.vue'
import bComCalcClassificationTable from '@/calcRule/BComCalcClassificationTable.vue'
import bidCalcBComTable from '@/calcRule/BidCalcBComTable.vue'
import bComCalcRelationEditorNew from '@/calcRule/BComCalcRelationEditorNew.vue'
import bComSeries from '@/calcRule/BComSeries.vue'
import { GetBidCalcSeries } from '@/api/bidCalcSeries'
import { GetBidCalcBCom } from '@/api/bidCalcBCom'
import bcomDecuctCond from '@/calcRule/BComDeductCond.vue'

// import { groupBy } from 'lodash-es'

import {  allAreas,deductOptions,newUUID  } from '@/calcRule/calc_config'

export default {
    components: { bcomSortCondEditor, bcomSortCond, bComCalcRelationEditorNew, bComCalcAttributeTable, bComSeries, bComCalcClassificationTable,bidCalcBComTable,bcomDecuctCond },
    setup() {
        const dTable = ref<VxeTable>({});
        const toolbarRef=ref<VxeToolbar>({});
        const classificationProps = { id: 'id', label: 'label', children: 'children', isLeaf: 'isLeaf', value: 'id' };
        const cprops = { multiple: true, checkStrictly: true};
        return { dTable,toolbarRef,classificationProps,cprops,allAreas,deductOptions }
    },
    data() {
        return {
            sortDataLoading: true,
            switchLoading: false,
            /**
             * 扣减规则
             */
            currentData:[],
            /**
             * 规则关联地区
             */
            currendDataSeries: [],
            /**
             * 列表数据
             */
            tableData: [],
            /**
             * 所有地区构件数量
             */
            areaCounts: [],
            /**
            * 当前选中地区
            */
            currentArea: '北京',
            /**
             * 只显示当前地区数据
             */
            showCurrentArea:false,
            /**
             * 当前系列
             */
            currentSeries: null,
            /**
             * 当前系列类型  1 定额 2清单
             */
            currentSeriesType:1,
            /**
             * 所有定额系列
             */
            allSeries: [],
            /**
             * 定额系列
             */
            deSeries:[],
            /**
             * 清单系列
             */
            qdSeries:[],
            /**
             * 当前地区所有定额系列
             */
            areaSeries: [],
            /**
             * 操作目标系列
             */
            opAreaSeries: null,
            /**
             * 属性
             */
            bidAttributes: [],
            /**
            * 属性Map
            */
            bidAttributesMap: null,
            /**
            * 属性值Map
            */
            bidAttributesValuseMap: null,
            /**
             * 编辑弹层
             */
            rowEditerShow: false,
            /**
             * 虚拟触发编辑按钮
             */
            editRef: null,
            /**
             * 构件属性设置是否显示
             */
            bComAttributeTableShow: false,

            /**
             * 操作类型  1 复制地区   3 复制数据
             */
            opType: 1,
            /**
             * 操作按钮
             */
            opRef: null,
            /**
             * 操作弹窗
             */
            opShow: false,
            /**
             * 操作系列
             */
            opSeries: null,
            /**
             * 操作目标构件
             */
            opClassification:null,
            /**
             * 复制源构件
             */
            copySourceClassification:null,
            /**
             * 定额系列弹窗
             */
            bComSeriesTableShow: false,
            /**
             * 构件分类弹窗
             */
            bComCalcClassificationTableShow: false,
            /**
             * 构件弹窗
             */
            bidCalcBComTableShow: false,
            /**
             * 当前构件分类
             */
            currentClassification: [],
            /**
             * 构件分类
             */
            classifications: [],
            /**
             * 计算规则构件
             */
            calcBComs:[],
            /**
             * 计算规则构件Map
             */
            calcBComsMap:null,
            /**
            * 当前构件
            */
            currentBComs: [],
            /**
             * 能否上移
             */
            canSortToTop: false,
            /**
             * 能否下移
             */
            canSortToBottom: false,
            /**
             * 选中行排序
             */
            currentRowSerial: null,
            /**
             * 所有构件
             */
            bcomOptions:[],
            /**
             * 展开行key(id)
             */
            treeExpandKeys:[],
            /**
             * 地区锁
             */
            areaLock:false,
            lockLoding:false
        }
    },
    async mounted() {
        this.dTable.connect(this.toolbarRef);
        await this.loadBComAttributes();
        await this.loadSeries();
        await this.loadClassification();
        await this.loadCalcBCom();
        await this.loadAllCalcBCom();
        if (!this.currentSeries) {
            this.currentSeries = this.allSeries?.filter(s => s.area == this.currentArea)[0]?.id ?? null;
        }
        await this.loadSortData();

        this.areaLock= await GetLocked(this.currentArea);
    },
    methods: {
        /**
         * 加载扣减关系排序数据并更新表格
         * @returns {Promise<void>}
         * 
         * 功能说明:
         * 1. 根据当前系列和分类获取构件关系数据
         * 2. 如果只显示当前地区，过滤出关联当前系列的数据
         * 3. 对数据进行树形结构转换
         * 4. 更新表格数据并管理加载状态
         */
        async loadSortData() {
            // 开始加载状态
            this.sortDataLoading = true;
            // 获取当前系列类型(定额/清单)
            this.currentSeriesType = this.allSeries.find(s => s.id == this.currentSeries)?.type;
            // 查询扣减关系数据
            this.currentData = await QueryByClassifications(this.currentSeries,this.currentClassification);
            await this.loadDataSeries();
            await this.reloadDataSeries();
            // 结束加载状态
            this.sortDataLoading = false;
        },
        async loadDataPart(classification) {
            this.sortDataLoading = true;
            try {
                // 查询构件关系数据
                let data = await QueryByClassifications(this.currentSeries,[[classification]]);
                this.currentData=this.currentData.filter(d=>d.classification!=classification);
                this.currentData.push(...data);
             
                await this.reloadDataSeries(classification);
            } catch (error) {
                console.error('局部刷新数据失败:', error);
                ElMessage({type: 'error',message: '刷新数据失败，请重试'});
            } finally {
                this.sortDataLoading = false;
            }
        },
        /**
         * 加载所有规则关联地区
         */
        async loadDataSeries(classification=null){
            if(this.currendDataSeries?.length>0 && classification){
                let temp_series=await GetAllSeries(this.currentSeries,Array.isArray(classification)? classification: [[classification]]);
                this.currendDataSeries=this.currendDataSeries.map(d=>{
                    let temp_data=temp_series.find(ts=>ts.id==d.id);
                    if(temp_data){
                        d.all_series=temp_data.all_series;
                        d.connected=d.all_series?.includes(this.currentSeries);
                    }
                    return d;
                });
            }else{
                this.currendDataSeries= await GetAllSeries(this.currentSeries,this.currentClassification);
            }
        },
        async currentSeriesChange(){
            this.sortDataLoading = true;
            this.currentSeriesType = this.allSeries.find(s => s.id == this.currentSeries)?.type;
            this.tableData.forEach(d=>{
                if(d.all_series){
                    d.connected=d.all_series?.includes(this.currentSeries);
                }
            });
            this.sortDataLoading = false;
        },
        /**
         * 重新加载所有规则关联地区
         */
        async reloadDataSeries(classification=null){
            //开始加载状态
            this.sortDataLoading = true;
            if(Array.isArray(classification)){
                classification=classification.flatMap(c=>c[0]);
            }
            //刷新关联地区信息
            this.currentData.forEach(d=>{
                if(!classification || (Array.isArray(classification)?classification?.includes(d.classification):d.classification==classification)){
                    var temp_data=this.currendDataSeries.find(s=>s.id==d.id);
                    if(temp_data){
                        d.all_series=temp_data.all_series;
                        d.connected=d.all_series?.includes(this.currentSeries);
                    }
                }
            });
            let showData=[];
            if(this.showCurrentArea) {
                showData = this.currentData.filter(d => d.all_series?.includes(this.currentSeries));
            }
            else{
                showData= this.currentData.map(d=>d);
            }
            this.tableData = this.initTreeNodes(showData);
            this.sortDataLoading = false;
        },
        switchCurrenArea(){
            this.sortDataLoading = true;
            if(this.showCurrentArea) {
                showData = this.currentData.filter(d => d.all_series?.includes(this.currentSeries));
            }
            else{
                showData= this.currentData.map(d=>d);
            }
            this.tableData = this.initTreeNodes(showData);
            this.sortDataLoading = false;
        },
        /**
         * 初始化树形节点数据
         * @param {Array} data 原始数据
         * @returns {Array} 处理后的树形数据
         * 1. 为每个数据项设置parentId指向其所属构件
         * 2. 收集所有构件节点(level=2)
         * 3. 根据当前选择的分类过滤分类数据
         * 4. 添加分类节点(level=1)作为树的顶层节点
         */
        initTreeNodes(data){
            let tempBComs=[];
            // 处理原始数据，设置parentId并收集构件节点
            data.forEach(d => {
                d.parentId=d.bcom.toString();
                // 如果构件节点还未收集，则添加到临时数组
                if(tempBComs.findIndex(b=>b.id==d.bcom)==-1){
                    tempBComs.push({
                        id:d.bcom,
                        parentId:d.classification.toString(),
                        classification:d.classification,
                        level:2, // 构件节点层级
                        bcom:d.bcom,
                        name:this.formatBCom(d.bcom)
                    });
                }
            });
            
            // 将构件节点添加到数据开头
            if(tempBComs.length>0)data.unshift(...tempBComs);
            
            // 处理分类数据
            let tempClassifications=this.classifications;
            // 如果有选中的分类，则过滤出选中的分类
            if(this.currentClassification.length>0){
                tempClassifications=this.classifications.filter(c=>
                    this.currentClassification.map(d=>d[0]).includes(c.id));
            }
            
            // 添加分类节点到数据开头
            data.unshift(...tempClassifications.map(c=>{
                return {
                    id:c.id.toString(),
                    classification:c.id,
                    parentId:null, // 顶层分类节点没有父节点
                    level:1, // 分类节点层级
                    name:c.name
                }
            }));
            
            return data;
        },
        /**
         * 地区更改
         */
         async areaChange() {
            this.sortDataLoading = true;
            this.areaSeries = this.allSeries?.filter(s => s.area == this.currentArea);
            this.currentSeries = this.areaSeries[0]?.id ?? null;
            this.opSeries=null;
            this.areaLock= await GetLocked(this.currentArea);
            this.tableData.forEach(d=>d.connected=d.all_series?.includes(this.currentSeries));
            this.sortDataLoading = false;
        },
        /**
         * 加载所有地区和构件数量
         */
        async loadAreaCounts() {
            this.areaCounts = await QueryAreaCount();
        },
        /**
         * 在不同地区系列间复制数据的方法
         * @returns {Promise<void>}
         *
         * 功能说明：
         * 1. 在用户确认后，将当前系列的数据复制到目标系列
         * 2. 支持指定目标构件范围进行复制
         * 3. 提供复制操作的用户反馈
         *
         * 步骤：
         * 1. 显示确认对话框，展示源系列和目标系列信息
         * 2. 调用API执行数据复制
         * 3. 根据复制结果更新界面和提示信息
         *
         * 注意事项：
         * - 需要验证源系列和目标系列的有效性
         * - 使用自定义样式确保对话框内容清晰可读
         * - 提供操作取消的处理机制
         */
        async copyData() {
            // 获取源系列和目标系列的显示标签
            let fromSeriesLabel = this.areaSeries.find(s => s.id == this.currentSeries)?.series;
            let toSeriesLabel = this.areaSeries.find(s => s.id == this.opSeries)?.series;

            // 显示确认对话框
            ElMessageBox.confirm(`从\r\n${fromSeriesLabel}\r\n复制到\r\n${toSeriesLabel}`,'复制数据',
            {
                confirmButtonText: '确认',
                cancelButtonText: '取消',
                type: 'warning',
                customClass: 'copyConfirm'
            }).then(async () => {
                // 执行数据复制
                let result = await TransferData({
                    fromseries: this.currentSeries,
                    series: this.opSeries,
                    targetBComs: this.opClassification
                });
                // 处理复制结果
                if (result > 0) {
                    await this.loadDataSeries(this.opClassification);
                    await this.reloadDataSeries(this.opClassification);
                    ElMessage({type: 'success',message: '复制完成!'});
                    
                }
                else{
                    ElMessage({type: 'warning',message: '未更改!'});
                }
                this.copyAreaShow = false;
            }).catch(async () => {
                // 处理取消操作
                ElMessage({type: 'info',message: '取消操作'});
            });
        },
        /**
         * 处理表格行的上下移动排序
         * @param {boolean} moveUp true表示上移，false表示下移
         *
         * 功能说明：
         * 1. 处理不同层级节点（分类节点/构件节点）的排序
         * 2. 同时更新关联数据的排序
         * 3. 维护树形结构的完整性
         *
         * 处理流程：
         * 1. 获取当前行及其同级节点
         * 2. 计算新的位置并移动
         * 3. 特殊处理构件节点（level=2）的子节点排序
         * 4. 保存新的排序并更新UI
         */
        async changeSerial(moveUp) {
            // 获取当前选中的行
            let tempRow = this.dTable.getCurrentRecord();
            
            // 获取同级节点的ID数组（根据层级过滤）
            let idArrray = this.tableData.filter(d =>
                d.level == tempRow.level &&
                (tempRow?.level == 2
                    ? d.parentId == tempRow.parentId        // 构件节点：同父节点
                    : d.classification == tempRow.classification  // 其他节点：同分类
                )
            ).map(d => d.id);

            // 计算当前位置和目标位置
            let currentIndex = idArrray.findIndex(d => d == tempRow.id);
            let newIndex = currentIndex + (moveUp ? -1 : 1);

            // 移动数组元素到新位置
            idArrray.splice(newIndex, 0, idArrray.splice(currentIndex, 1)[0]);

            // 如果是构件节点，需要处理其下所有子节点的排序
            if(tempRow?.level == 2) {
                let tmepIds = [];
                // 收集所有相关子节点的ID
                idArrray.forEach(id => {
                    tmepIds.push(
                        ...this.tableData
                            .filter(d => d.parentId == id && !d.level)
                            .map(d => d.id)
                    );
                });
                idArrray = tmepIds;
            }

            // 更新排序并保存
            if (idArrray?.length > 0) {
                await UpdateSerial(idArrray);
            }
            
            // 重新加载数据并恢复选中状态
            await this.loadDataPart(tempRow.classification);
            await this.dTable.setCurrentRow(
                this.dTable.getRowById(tempRow.id)
            );
            
            // 显示成功提示
            ElMessage({type: 'success',message: '排序完成!'});
        },
        /**
         * 加载属性
         */
        async loadBComAttributes() {
            this.bidAttributes = await GetBComAttributes();
            this.bidAttributesMap = new Map(this.bidAttributes.map(d => [d.id, d.name]));
            this.bidAttributesValuseMap = new Map(this.bidAttributes.filter(d => d?.values?.length > 0).map(d => [d.id, d.values.split(',')]));
        },
        /**
         * 加载当前地区定额系列
         */
        async loadSeries() {
            this.allSeries = await GetBidCalcSeries();
            this.deSeries=this.allSeries.filter(d=>d.type==1);
            this.qdSeries=this.allSeries.filter(d=>d.type==2);
            let tempSeries= this.allSeries.map(s=>{return {id:s.id,area:s.area,series:s.series,label:s.remarks,type:s.type}});
            this.allSeries=tempSeries.sort((a,b)=> allAreas.indexOf(a.area)- allAreas.indexOf(b.area));
            this.areaSeries = this.allSeries?.filter(s => s.area == this.currentArea);
        },
        /**
         * 修改地区
         * @param {string} val 
         */
        async loadOpAreaSeries(val) {
            this.opAreaSeries = this.allSeries?.filter(s => s.area == val);
            this.opSeries = this.opAreaSeries?.filter(s => s.id != this.currentSeries)[0]?.id ?? null;
        },
        /**
         * 保存数据
         * @param {Object} data 要保存的数据对象
         * 1. 关闭编辑弹窗并显示加载状态
         * 2. 深拷贝数据以避免修改原始数据
         * 3. 获取新的排序序号
         * 4. 处理更新或新增逻辑
         * 5. 调用API保存数据
         * 6. 重新加载数据并更新UI
         */
        async saveData(data) {
            // 关闭编辑弹窗并设置加载状态
            this.rowEditerShow = false;
            this.sortDataLoading=true;
            
            // 深拷贝数据以避免修改原始数据
            let tempData = cloneDeep(data);
            // 获取新的排序序号
            tempData.serial = this.getNewSerial(data);
            
            // 确保有构件数据
            if (tempData?.bcom) {
                // 更新操作 - 已有ID
                if (tempData?.id) {
                    // 找到对应的行并更新本地数据
                    let tempRow = this.tableData.find(d => d.id == tempData.id);
                    tempRow.bcom = tempData.bcom;
                    tempRow.attr_conds = tempData.attr_conds;
                    tempRow.target_bcom = tempData.target_bcom;
                    tempRow.target_attr_conds = tempData.target_attr_conds;
                    tempRow.operation = tempData.operation;
                    tempRow.deduct_type = tempData.deduct_type;
                    tempRow.remarks = tempData.remarks;
                    tempRow.serial = tempData.serial;
                    tempRow.deduct_settings = tempData.deduct_settings;
                }
                // 新增操作 - 没有ID
                else {
                    // 检查是否已存在相同数据
                    let isExists = await ExistsAsync({
                        id: '-1',
                        bcom: tempData.bcom,
                        attr_conds: tempData.attr_conds ? JSON.stringify(tempData.attr_conds) : '[]',
                        target_bcom: tempData.target_bcom,
                        target_attr_conds: tempData.target_attr_conds ? JSON.stringify(tempData.target_attr_conds) : '[]',
                        deduct_settings: tempData.deduct_settings ? JSON.stringify(tempData.deduct_settings) : '[]',
                    });
                    
                    // 如果已存在则提示错误
                    if (isExists === true) {
                        ElMessage({ type: 'error', message: '当前构件关系已存在!' });
                        this.sortDataLoading=false;
                        return false;
                    }
                    
                    // 为新数据生成唯一ID
                    tempData.id =newUUID();
                }
                
                // 准备要保存的数据
                let postData = {
                    id: tempData.id,
                    bcom: tempData.bcom,
                    attr_conds: tempData.attr_conds ? JSON.stringify(tempData.attr_conds) : '[]',
                    target_bcom: tempData.target_bcom,
                    target_attr_conds: tempData.target_attr_conds ? JSON.stringify(tempData.target_attr_conds) : '[]',
                    deduct_type: tempData.deduct_type,
                    operation: tempData.operation,
                    remarks: tempData.remarks,
                    serial: tempData.serial,
                    deduct_settings:tempData.deduct_settings ? JSON.stringify(tempData.deduct_settings) : '[]',
                };
                
                // 调用API保存数据
                await SaveAsync(postData);

                await this.loadDataSeries(data.classification);
                //重新加载当前分类
                await this.loadDataPart(data.classification);

                //新增 更新排序
                if(tempData.id!=postData.id){
                    // 更新分类下的所有行的排序
                    let ids = this.tableData.filter(d => d.classification == data.classification).map(d => d.id);
                    if (ids?.length > 0) await UpdateSerial(ids);
                    for (let index = 0; ids < ids.length; index++) {
                        const id = ids[index];
                        let temp_data=this.tableData.find(d=>d.id==id);
                        if(temp_data)temp_data.serial=index;
                    }
                    // 滚动到保存的行并显示成功消息
                    this.dTable.scrollToRow(this.dTable.getRowById(postData.id));
                    // 关闭编辑弹窗并重新加载数据
                    this.rowEditerShow = false;
                }

                ElMessage({ type: 'success', message: '保存成功!' });
                this.sortDataLoading=false;
            }
        },
        /**
         * 复制关联行
         * @param {Object} data 
         */
        /**
         * 复制并创建构件关系的反向关联
         * @param {Object} data 源构件关系数据
         * 该方法会创建一个新的构件关系，其中：
         * 1. 将源构件和目标构件的角色互换
         * 2. 保持原有的属性条件和扣减设置
         * 3. 自动分配新的序列号
         * 4. 更新相关数据的排序
         */
        async copyConnRow(data){
            // 创建源数据的深拷贝，避免修改原始数据
            let tempData = cloneDeep(data);
            
            // 检查是否已存在反向关联关系
            let isExists = await ExistsAsync({
                id: '-1',
                // 交换源构件和目标构件的位置
                bcom: tempData.target_bcom,
                attr_conds: tempData.target_attr_conds ? JSON.stringify(tempData.target_attr_conds) : '[]',
                target_bcom: tempData.bcom,
                target_attr_conds: tempData.attr_conds ? JSON.stringify(tempData.attr_conds) : '[]',
                deduct_settings: tempData.deduct_settings ? JSON.stringify(tempData.deduct_settings) : '[]',
                operation: tempData.operation,
                deduct_type: tempData.deduct_type
            });

            // 如果已存在反向关联，提示错误并返回
            if (isExists === true) {
                ElMessage({ type: 'error', message: '当前构件已经存在!' });
                return false;
            }

            // 获取目标构件的分类ID，用于排序和组织
            let temp_classification = this.calcBComs.find(d => d.id == Number(data.target_bcom))?.classification_id;

            // 准备新的构件关系数据
            let postData = {
                id: newUUID(), // 生成新的唯一标识符
                // 交换源构件和目标构件的所有相关属性
                bcom: tempData.target_bcom,
                attr_conds: tempData.target_attr_conds ? JSON.stringify(tempData.target_attr_conds) : '[]',
                target_bcom: tempData.bcom,
                target_attr_conds: tempData.attr_conds ? JSON.stringify(tempData.attr_conds) : '[]',
                deduct_type: tempData.deduct_type,
                operation: tempData.operation,
                remarks: tempData.remarks,
                serial: 0, // 初始序号设为0
                deduct_settings: tempData.deduct_settings ? JSON.stringify(tempData.deduct_settings) : '[]',
            };

            // 获取同一分类中最后一个相似构件的序号
            let lastRow = this.tableData.findLast(d =>
                d.classification == temp_classification && d.bcom == postData.bcom
            );
            if(lastRow){
                postData.serial = lastRow.serial; // 使用最后一个构件的序号
            }
            // 保存新的构件关系
            await SaveAsync(postData);

            //重新加载当前分类
            await this.loadDataPart(temp_classification);

           // 更新同一分类下所有构件的排序
           let ids = this.tableData.filter(d =>d.classification == temp_classification).map(d => d.id);
           if (ids?.length > 0) {
            await UpdateSerial(ids);
            }
            ElMessage({ type: 'success', message: '保存成功!' });
        },
        /**
         * 删除数据
         * @param {Object} row 
         */
        async DeleteData(row) {
            ElMessageBox.confirm('确认删除?', 'Warning', {
                confirmButtonText: '确认',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(async () => {
                await DeleteAsync({ val: row.id });
                await this.loadDataPart(row.classification);
                // this.tableData = this.tableData.filter(d => d.id != row.id);
                ElMessage({ type: 'success', message: '删除成功!' });
            })
                .catch(() => { });
        },
        /**
         * 显示行编辑弹框
         * @param {Object} btn 触发按钮
         * @param {Object} row 行数据
         */
        showEditor(btn, row) {
            this.editRef = btn;
            this.rowEditerShow = true;
            this.$refs.relationEditor.changeData(cloneDeep(row));
        },
        /**
         * 显示操作弹窗
         * @param {Object} btn 触发按钮
         * @param {Number} optype 类型 1复制地区  3复制数据
         */
        showOp(btn, optype) {
            this.opRef = btn;
            this.opType = optype;
            this.opAreaSeries = this.areaSeries;
            this.opShow = true;
        },
        /**
         * 加载构件分类
        */
        async loadClassification() {
            this.classifications = await GetBidCalcBCom(true);
            await this.loadAllCalcBCom();
        },
        /**
         * 加载构件
        */
        async loadCalcBCom() {
            this.calcBComs = await GetBidCalcBCom();
            this.calcBComsMap = new Map(this.calcBComs.map(d => [d.id, d.alias]));
            await this.loadAllCalcBCom();
        },
        /**
         * 加载所有构件
        */
        async loadAllCalcBCom() {
            this.bcomOptions = this.classifications.map(c=>{ return {
                    value:c.id,
                    label:c.name,
                    children: this.calcBComs.filter(d=>d.classification_id==c.id).map(d=>{return {value:d.id,label:d.alias,isLeaf:true }})
                }
            });
        },
        /**
        * 合并单元格
        */
        mergeRowColumnMethod({ row, $rowIndex, _columnIndex, column, visibleData }) {
            let colIndexs = [0,1];
            if (colIndexs.includes(_columnIndex)&&(row.level==undefined||row.level==null)) {
                let tempIndex= visibleData.findIndex(d=>d.id==row.id);
                let prevRow = visibleData[tempIndex - 1];
                if (prevRow && !prevRow.level && prevRow[column.field] === row[column.field]) {
                    return { rowspan: 0, colspan: 0 }
                } 
                else {
                    let countRowspan = 0;
                    let tempColumns=this.dTable.getColumns();
                    for (let index = tempIndex; index < visibleData.length; index++) {
                        let data = visibleData[index];
                        let isSame = false;
                        let cols= colIndexs.filter(c=>c<=_columnIndex);
                        for (let cIndex = 0; cIndex < cols.length; cIndex++) {
                            let tempField=tempColumns[cols[cIndex]].field;
                            isSame = data[tempField] === row[tempField];
                            if(!isSame)break;
                        }
                        if(isSame){
                            countRowspan++;
                        }
                        else break;
                    }
                    if (countRowspan > 1) {
                        return { rowspan: countRowspan, colspan: 1 };
                    }
                }
                // let tempArray=visibleData.filter(d=>d.parentId==row.parentId&&d[column.field]==row[column.field]);
                // let tempIndex= tempArray.findIndex(d=>d.id==row.id);
                // if(tempIndex==0){
                //     return { rowspan:tempArray.length, colspan: 1}
                // }
                // else{
                //     return { rowspan: 0, colspan: 0 }
                // }
            }
        },
        /**
         * 当前行变化
         * @param {Object} row 行数据
         */
        /**
         * 处理表格当前行变化的方法
         * @param {Object} params 包含当前行数据的参数对象
         *
         * 该方法主要用于：
         * 1. 根据当前视图模式和行类型控制排序按钮的可用状态
         * 2. 处理不同层级节点的排序逻辑
         * 3. 更新当前行的序号信息
         */
        currentChange({ row }) {
            // 当只显示当前地区时，禁用所有排序功能
            if(this.showCurrentArea){
                this.canSortToTop = false;
                this.canSortToBottom = false;
                return;
            }

            let tempArray = [];

            // 处理顶层分类节点，禁用排序功能
            if(row?.level == 1){
                this.canSortToTop = false;
                this.canSortToBottom = false;
            }
            else{
                // 处理二级构件节点
                if(row?.level == 2){
                    // 获取同一父节点下的所有构件
                    tempArray = this.tableData.filter(d =>
                        d.parentId == row.parentId && d.level == 2
                    );
                }
                // 处理实际的构件规则节点
                else{
                    // 保存当前行的序号
                    this.currentRowSerial = row.serial;
                    // 获取同一构件下的所有规则
                    tempArray = this.tableData.filter(d =>
                        d.bcom == row.bcom && d.level != 2
                    );
                }

                // 计算当前行在筛选结果中的位置
                let index = tempArray.findIndex(d => d.id == row.id);
                
                // 根据位置设置上下移动按钮的状态：
                // - 第一行不能向上移动
                // - 最后一行不能向下移动
                this.canSortToTop = index != 0;
                this.canSortToBottom = index != tempArray.length - 1;
            }
        },
        /**
         * 为新行获取合适的排序序号
         * @param {Object} row 要处理的行数据，包含classification、bcom、target_bcom和serial属性
         * @returns {Number} 返回计算得到的排序序号
         *
         * 计算逻辑：
         * 1. 首先尝试使用传入行的现有序号
         * 2. 查找同分类、同源构件、同目标构件的所有非层级节点的序号
         * 3. 如果现有序号不在查找结果中，使用最后一个序号
         * 4. 如果没有找到相关序号，查找最后一条匹配记录的序号
         *
         * 使用场景：
         * - 添加新的构件关系记录时
         * - 复制现有构件关系时
         * - 更新构件关系时维持正确的排序
         */
        getNewSerial(row) {
            // 获取初始序号
            let tempSerial = row.serial;
            
            // 获取同条件下所有非层级节点的序号列表
            let serials = this.tableData.filter(d => 
            d.classification == row.classification && 
            d.bcom == row.bcom && 
            d.target_bcom == row.target_bcom && 
            !d.level
            ).map(d => d.serial);

            if(serials?.length > 0) {
            // 如果当前序号不在列表中，使用最后一个序号
            if(!serials.includes(tempSerial)) {
                tempSerial = serials[serials.length-1];
            }
            } else {
            // 如果没有序号列表，查找最后一条匹配记录
            let lastRow = this.tableData.findLast(d => 
                d.classification == row.classification && 
                d.bcom == row.bcom && 
                d.target_bcom == row.target_bcom && 
                !d.level
            );
            if(lastRow) {
                tempSerial = lastRow.serial;
            }
            }
            
            return tempSerial;
        },
        /**
         * 格式化构件分类
         * @param {Object} row 行数据
         * @returns {String} 构件分类名称
         */
        formatClassification({row}){
            return this.classifications.find(d=>d.id==row.classification)?.name;
        },
        /**
         * 格式化构件
         * @param {String} bcom 构件ID
         * @returns {String} 构件名称
         */
        formatBCom(bcom){
            if(bcom)return this.calcBComs.find(c=>c.id==Number(bcom))?.alias;
        },
        /**
         * 格式化构件名称
         * @param {Object} row 行数据
         * @returns {String} 构件名称
         */
        formatName({row}){
           return row?.name?? row?.bcom?.split(',')?.map(c=>this.formatBCom(c)).join(',');
        },
        /**
         * 格式化目标构件
         * @param {Object} row 行数据
         * @returns {String} 目标构件名称
         */
        formatTargetBCom({row}){
            return this.formatBCom(row?.target_bcom);
        },
        /**
         * 复制构件数据和关联关系
         *
         * 功能说明：
         * 1. 在不同构件之间复制规则关系配置
         * 2. 保持构件分类的层级关系
         * 3. 自动更新数据并反馈结果
         *
         * 前置条件：
         * - 必须同时选择源构件和目标构件
         * - 源构件和目标构件必须是二级节点（length==2）
         *
         * 处理流程：
         * 1. 验证源和目标构件的选择
         * 2. 调用API复制数据
         * 3. 刷新表格数据
         * 4. 关闭操作弹窗
         */
        async copyRowData() {
            // 验证源构件和目标构件的选择是否完整
            if (this.copySourceClassification?.length == 2 &&
                this.opClassification?.length == 2) {
                
                // 调用API复制数据
                await CopyRowData(
                    // 源构件ID
                    this.copySourceClassification[1].toString(),
                    // 目标构件ID
                    this.opClassification[1].toString(),
                    // 目标构件的分类ID
                    this.opClassification[0]
                );
                
                // 重新加载数据
                await this.loadSortData();
                
                // 显示成功提示
                ElMessage({
                    type: 'success',
                    message: '复制完成!'
                });
            }
            
            // 关闭操作弹窗
            this.opShow = !this.opShow;
        },
        /**
         * 保存构件关联地区
         */
         async saveRowRelationArea(row){
            await SaveBidCalcRelationAreas(row.id,row.temp_series);
            row.all_series=row.temp_series;
            this.dTable.clearEdit();
            ElMessage({ type: 'success', message: '保存成功!' });
        },
        /**
         * 显示构件关联地区
         */
        /**
         * 生成地区关联的悬浮提示内容
         * @param {Object} params 包含行数据和列索引
         *   - row: 当前行数据
         *   - columnIndex: 当前列索引
         * @returns {String|undefined} 返回提示内容或undefined
         *
         * 该方法用于：
         * 1. 仅在关联地区列（第5列）显示提示
         * 2. 当关联地区数量超过5个时显示完整列表
         * 3. 以逗号分隔的形式展示所有关联地区
         */
        showAreaTooltip({row, columnIndex}) {
            // 仅处理关联地区列
            if(columnIndex == 5) {
                // 获取当前类型（定额/清单）的系列
                let current_series = this.getSeriesByCurrentType(row?.all_series);
                
                // 当关联地区超过5个时显示完整列表
                if(current_series?.length > 5) {
                    // 返回所有关联地区的名称，以逗号分隔
                    return this.allSeries
                        .filter(s => current_series.includes(s.id))
                        .map(s => s.label)
                        .join(' , ');
                }
            }
            // 其他情况不显示提示
            return undefined;
        },
        /**
         * 记录展开节点
         */
         /**
          * 管理树形结构的展开状态记录
          * @param {Object} params 展开状态参数
          *   - expanded: 是否展开
          *   - row: 当前操作的行数据
          *
          * 该方法用于：
          * 1. 维护已展开节点的列表(treeExpandKeys)
          * 2. 在页面刷新后保持节点的展开状态
          * 3. 支持树形结构的交互体验
          */
         treeExpand({expanded, row}) {
             // 处理展开操作
             if(expanded) {
                 // 将当前节点ID添加到展开列表
                 this.treeExpandKeys.push(row.id);
             }
             // 处理折叠操作
             else {
                 // 查找节点在展开列表中的位置
                 let keyIndex = this.treeExpandKeys.indexOf(row.id);
                 // 如果找到，则从展开列表中移除
                 if(keyIndex > -1) {
                     this.treeExpandKeys.splice(keyIndex, 1);
                 }
             }
         },
        /**
         * 根据行数据动态计算表格行的样式类
         * @param {Object} row 当前行数据，包含构件信息和备注等
         * @param {Number} $rowIndex 当前行在表格中的索引位置
         * @returns {String} 返回CSS类名字符串，控制行的显示样式
         *
         * 样式规则：
         * - merge-tr: 当相邻行的目标构件不同时添加，用于视觉分组
         * - remarks-tr: 当行包含备注信息时添加，用于高亮显示
         *
         * 注意：
         * 1. 仅在非层级节点间进行样式处理
         * 2. 多个样式类会以空格分隔
         */
        mergeRowClass({row, $rowIndex}) {
            // 初始化样式类字符串
            let temp_class = '';
            
            // 获取下一行数据用于比较
            let nextRow = this.dTable.getTableData().visibleData[$rowIndex + 1];
            
            // 检查是否需要添加分组样式
            // - nextRow存在
            // - 当前行和下一行都不是层级节点
            // - 两行的目标构件不同
            if (nextRow != null &&
                !row?.level &&
                !nextRow?.level &&
                row.target_bcom != nextRow.target_bcom) {
                temp_class += 'merge-tr';
            }
            
            // 如果行包含备注信息，添加高亮样式
            if (row.remarks?.length > 0) {
                // 如果已有样式，添加空格分隔符
                temp_class += (temp_class ? ' ' : '') + 'remarks-tr';
            }
            
            return temp_class;
        },
        /**
         * 单元格双击
         * @param {Object} row 行数据
         * @param {Number} columnIndex 列索引
         */
        /**
         * 处理单元格双击事件
         * @param {Object} params 双击事件参数对象
         *   - row: 当前行数据
         *   - columnIndex: 被双击的列索引
         *
         * 处理逻辑：
         * 1. 对于非层级节点(非分类和构件节点)且非第5列(关联地区列)
         *    - 触发行编辑功能
         * 2. 对于层级节点或第5列
         *    - 切换树形结构的展开/折叠状态
         *    - 更新展开节点的记录状态
         */
        async cellDBClick({ row, columnIndex }){
            // 处理编辑情况：非层级节点且非关联地区列
            if(!row.level && columnIndex != 5) {
                // 通过引用触发编辑按钮的点击事件
                this.$refs[`edit-${row.id}`].$el.click();
            }
            // 处理展开/折叠情况：层级节点或关联地区列
            else {
                // 切换节点的展开状态
                await this.dTable.toggleTreeExpand(row);
                // 更新展开节点的记录
                this.treeExpand({
                    expanded: this.dTable.isTreeExpandByRow(row),
                    row
                });
            }
        },
        /**
         * 处理复选框状态变化事件
         * @param {Object} params 包含checked(是否选中)和row(行数据)的参数对象
         * 该方法处理构件与地区的关联关系：
         * 1. 对于分类节点(level=1)，影响其下所有构件
         * 2. 对于构件节点(level=2)，影响其直接子节点
         * 3. 对于普通节点，仅影响当前行
         * 4. 根据选中状态，创建或删除关联关系
         */
         async checkBoxChange({checked, row}) {
            this.sortDataLoading = true;
            //存储需要处理的所有行
            let temp_rows = [];
            
            //设置当前行为选中状态
            await this.dTable.setCurrentRow(row);
            
            //根据节点层级确定需要处理的行
            if(row.level && row?.children?.length > 0) {
                if(row.level == 1) {
                    //对于顶层分类，获取所有孙子节点（实际的构件规则）
                    temp_rows=row.children.flatMap(d => d.children);
                } else {
                    //对于构件节点，获取其直接子节点
                    temp_rows=row.children;
                }
                  // 更新所有相关行的连接状态
                  temp_rows.forEach(r =>{
                    r.oldConnected =  r.connected;
                    r.connected = checked;
                });
            }
          
            
            if (checked) {
                // 定义保存关联关系的函数
                let saveRelation = async (rows) => {
                    // 保存构件与当前系列的关联
                    await SaveBidCalcRelationAreasBySeries(this.currentSeries,rows.map(r => r.id));
                    // 更新每个构件的系列关联信息
                    rows.forEach(r => {
                        if (r?.all_series) {
                            if(!r.all_series.includes(this.currentSeries)) {
                                r.all_series.push(this.currentSeries);
                            }
                        } else {
                            r.all_series = [this.currentSeries];
                        }
                    });
                    await this.loadDataSeries(row.classification);
                    ElMessage({type: 'success',message: `规则已关联到${this.currentArea}!`});
                };
                
                // 如果影响多行，需要用户确认
                if(temp_rows.length > 1) {
                    await ElMessageBox.confirm(
                        `是否将 ${row.name} 下所有规则都关联到${this.currentArea}`,
                        "提示",
                        {
                            confirmButtonText: '确认',
                            cancelButtonText: '取消',
                            type: 'error'
                        }
                    ).then(async () => {
                        await saveRelation(temp_rows);
                    }).catch(async () => {
                        row.connected = false;
                        // 取消操作时恢复选中状态
                        temp_rows.forEach(r => {
                            r.connected = r.oldConnected;
                            delete r.oldConnected;
                        });
                        ElMessage({ type: 'info', message: '取消操作!' });
                    });
                } else {
                    // 单行直接保存
                    await saveRelation([row]);
                }
            } else {
                // 取消关联时的确认
                await ElMessageBox.confirm(
                    `是否取消关联到${this.currentArea}`,
                    "提示",
                    {
                        confirmButtonText: '确认',
                        cancelButtonText: '取消',
                        type: 'error'
                    }
                ).then(async () => {
                    // 删除关联关系
                    await DeleteBidCalcRelationAreasBySeries(this.currentSeries,temp_rows?.length > 0 ? temp_rows.map(r => r.id) : [row.id]);
                    await this.loadDataSeries(row.classification);
                    if(temp_rows?.length > 0) {
                        // 从每个构件的系列列表中移除当前系列
                        temp_rows.forEach(r => {
                            r.all_series = r.all_series?.filter(s => s != this.currentSeries);
                        });
                    }
                    else{
                        row.all_series = row.all_series?.filter(s => s != this.currentSeries);
                    }
                    ElMessage({ type: 'success', message: '取消关联成功!' });
                }).catch(async () => {
                    row.connected = true;
                    temp_rows.forEach(r => {
                        r.connected = r.oldConnected;
                        delete r.oldConnected;
                    });
                    // 取消操作时恢复选中状态
                    ElMessage({type: 'info',message: '取消操作!'});
                });
            }
            // 重新加载所有被修改的行
            for (let index = 0; index < temp_rows.length; index++) {
                const r = temp_rows[index];
                await this.dTable.reloadRow(r);
            }
            this.sortDataLoading = false;
        },
        /**
         * 处理地区锁定状态变更前的验证
         * 作为el-switch组件的before-change钩子函数，用于控制地区锁定的切换
         * @returns {Promise<boolean>} 返回Promise对象：
         *   - resolve(true): 允许切换锁定状态
         *   - reject(false): 阻止切换锁定状态
         */
        beforeLockChange(){
            // 设置加载状态
            this.lockLoding = true;

            return new Promise(async (resolve, reject) => {
                try {
                    // 显示密码输入对话框，根据当前状态显示不同提示
                    await ElMessageBox.prompt(
                        `${this.areaLock ? '锁定' : '解锁'} 地区?`,
                        "提示",
                        {
                            confirmButtonText: '确认',
                            cancelButtonText: '取消',
                            inputType: 'password',
                            inputPlaceholder: '请输入锁定密码',
                            type: 'warning'
                        }
                    ).then(async (cv) => {
                        // 验证输入的密码是否正确
                        var pass = await LockCheck(this.currentArea, cv.value);
                        
                        if(pass) {
                            // 密码正确，执行锁定/解锁操作
                            await ChangeLock(
                                this.currentArea,
                                this.areaLock,
                                // 锁定时保存密码，解锁时不保存(null)
                                this.areaLock ? cv.value : null
                            );
                            
                            // 显示成功消息
                            ElMessage({
                                type: 'success',
                                message: `${this.areaLock ? '锁定' : '解锁'}成功!`
                            });
                            
                            this.lockLoding = false;
                            resolve(true); // 允许切换状态
                        } else {
                            // 密码错误，显示错误消息
                            ElMessage({
                                type: 'error',
                                message: '密码错误!'
                            });
                            
                            this.lockLoding = false;
                            reject(false); // 阻止切换状态
                        }
                    }).catch(async () => {
                        // 用户取消操作，显示提示信息
                        ElMessage({
                            type: 'info',
                            message: '取消操作'
                        });
                        
                        this.lockLoding = false;
                        reject(false); // 阻止切换状态
                    });
                } catch (error) {
                    // 处理其他可能的错误情况
                    console.error('锁定状态变更失败:', error);
                    this.lockLoding = false;
                    reject(false);
                }
            });
        },
        /**
         * 根据当前系列类型筛选系列ID
         * @param {number[]} sids 需要筛选的系列ID数组
         * @returns {number[]} 返回当前类型下的系列ID数组
         *
         * 系列类型说明：
         * - currentSeriesType = 1：定额系列（deSeries）
         * - currentSeriesType = 2：清单系列（qdSeries）
         *
         * 功能：
         * 1. 根据当前系列类型选择对应的系列集合（定额/清单）
         * 2. 从传入的系列ID数组中筛选出属于当前类型的ID
         * 3. 用于在系列关联显示和编辑时确保系列类型的一致性
         */
        getSeriesByCurrentType(sids) {
            // 根据当前系列类型选择对应的系列集合
            let temp_series = this.currentSeriesType == 1
                ? this.deSeries    // 定额系列集合
                : this.qdSeries;   // 清单系列集合

            // 筛选出在当前类型系列集合中存在的ID
            return sids?.filter(s =>
                temp_series.map(s => s.id).includes(s)
            );
        },
    },
    provide() {
        return {
            /**
             * 构件属性
             */
            bidAttributes: computed(() => this.bidAttributes),
            /**
             * 构件属性映射
             */
            bidAttributesMap: computed(() => this.bidAttributesMap),
            /**
             * 构件属性值映射
             */
            bidAttributesValuseMap: computed(() => this.bidAttributesValuseMap),
            /**
             * 构件分类
             */
            classifications: computed(() => this.classifications),
            /**
             * 计算规则构件
             */
            calcBComs: computed(() => this.calcBComs),
            /**
             * 构件计算映射
             */
            calcBComsMap: computed(() => this.calcBComsMap),
            /**
             * 构件选项
             */
            bcomOptions: computed(() => this.bcomOptions),
        }
    }
}
</script>

<style scoped>
.bcom-header {
    padding: 10px;
    background-color: var(--el-color-info-light-9);
    height: 60px;
    display: block;
}

.title {
    display: flex;
    justify-content: center;

}

.link {
    display: flex;
    justify-content: right;
}
.link a{
    margin-left: 18px;
}

.bcom-choose {
    justify-content: left;
    align-items: center;
    background-color: white;
    padding: 0 0 10px 10px;
}

.bcom-choose .el-col {
    display: flex;
}

.bcom-choose .el-text {
    margin-right: 6px;
}

.bcom-choose .el-radio {
    margin-top: 5px;
    margin-right: 15px;
}

.bcom-choose .el-radio-group {
    max-width: 100%;
}

.bcom-card .bcom-from {
    padding: 18px 0;
}

.vxe-cell .el-button+.el-button {
    margin-left: 1px;
}

.deduct+.deduct {
    margin-left: 10px;
}


.conds {
    display: inline-flex;
    /* display: flex ; */
    align-items: center;
}

:deep(.sort-table table){
    border-collapse:collapse;
}

:deep(.sort-table .merge-tr){
    border-bottom:20px solid #f2f3f9;
}

:deep(.sort-table .row--stripe.remarks-tr){
    background-color: #fffca3 ;
}


:deep(.sort-table .row--stripe.remarks-tr.row--hover){
    background-color: #ffea48 ;
}

:deep(.sort-table .row--current .vxe-cell) {
    height: auto !important;
    max-height: none !important;
}

:deep(.row--current .attrs .vxe-cell) {
    overflow: visible;
    display: flex;
    flex-wrap: wrap;
}

:deep(.attrs .vxe-cell .el-tag) {
    white-space: break-spaces;
    margin-top: 3px;
    margin-bottom: 3px;
}

:deep(.row--current .attrs .vxe-cell .el-tag) {
    height: auto;
    min-height: 24px;
}

:deep(.deduct) {
    margin: 2px 0 2px 0;
    display:  inline-flex ;
    align-items: center;
}

:deep(.deduct+.deduct) {
    margin-left: 10px;
}

:deep(.deduct .el-text +.el-text) {
    margin-left: 5px;
}

:deep(.deduct .el-tag +.el-text) {
    margin-left: 3px;
}

:deep(.deduct .el-text +.el-tag){
    margin-left: 3px;
}

.rank-cell {
    display: flex;
    align-items: center;
}

.rank-cell .i-rank {
    width: 1em;
    height: 1em;
    margin-right: 3px;
    cursor: grab;
}

:deep(.sort-table .vxe-body--row.row--current) {
    background-color: #91d5f5;
}

:deep(.sort-table .vxe-body--row.row--hover.row--current) {
    background-color: #8bd3f5;
}


:deep(.bcom-choose .el-cascader){
    width: 300px;
}
:deep(.checkbox-cell .vxe-cell){
    display: flex;
    justify-content: center;
}
.area-series-select{
    width:82%;
    margin-right:5px;
}
:deep(.sort-table .el-button){
    height:24px;
    padding: 5px 9px ;
}
:deep(.sort-table .el-tag){
    height:20px;
}
:deep(.sort-table .area-tag){
    margin-bottom:2px;
}
:deep(.vxe-body--column){
    padding: 0 !important;
}
/* :deep(.vxe-body--expanded-row){
    background-color:#eee;
} */
</style>

<style>
.copyConfirm{
    --el-messagebox-width:500px !important;
}
.copyConfirm .el-message-box__message p{
    white-space:pre-wrap;
}
</style>