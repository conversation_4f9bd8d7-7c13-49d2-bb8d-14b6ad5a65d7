/**
 * 构件计算公式组件
 * 用于管理和配置构件的计算规则和公式
 * 包含构件选择、计算公式配置、条件设置等功能
 */
<template>
    <el-row style="height:100%">
        <el-col :span="24">
            <el-affix :offset="0">
                <el-row class="bcom-header">
                    <el-row class="title">
                        <el-text size="large" tag="b">构件计算公式</el-text>
                    </el-row>
                </el-row>
                <el-row class="bcom-choose">
                    <el-col :span="4">
                        <el-text>构件</el-text>
                        <el-cascader v-model="currentClassification" :props="cprops" placeholder="构件选择" :options="bcomOptions" 
                        filterable clearable collapse-tags collapse-tags-tooltip @change="loadSortData" :teleported="false">
                        <template #default="{node,data}">
                            <el-text v-if="data.conds?.length>0" type="success" >{{ data.label }}</el-text>
                            <span v-else>{{ data.label }}</span>
                        </template>
                        </el-cascader>
                    </el-col>
                    <el-col :span="8">
                        <el-button text icon="Operation" @click="formulaTableShow = !formulaTableShow">计算公式</el-button>
                        <el-button type="primary" @click="showEditor($event.target, {bcom: null, attr_conds: [], classification: null, calc_settings:[], remarks: null})">添加构件</el-button>
                    </el-col>
                    <el-dialog v-if="formulaTableShow" v-model="formulaTableShow" title="计算公式" width="60%" center align-center destory-on-close @closed="loadFormulas">
                            <bComCalcFormulaTable :ftype="2" />
                     </el-dialog>
                </el-row>
            </el-affix>
            <el-row>
                <el-col>
                    <vxe-table id="ruleTable" ref="dTable" :data="tableData" class="sort-table" show-overflow stripe
                        :tree-config="{expandAll: false,transform:true,expandRowKeys:treeExpandKeys}"
                        :loading="sortDataLoading" border height="840" :column-config="{ resizable: true }"
                        @cell-dblclick="cellDBClick"
                        :edit-config="{ trigger: 'dblclick', showStatus: true, mode: 'cell'}" 
                        :row-config="{ useKey: true, isHover: true, isCurrent: true, keyField: 'id', transform: true, height: '28' }"
                        :span-method="mergeRowColumnMethod"
                        :checkbox-config="{checkField:'connected'}"
                        @toggle-tree-expand="treeExpand"
                        :custom-config="customConfig"
                        >
                        <vxe-column title="计算规则构件" field="bcom" width="210" align="center" :formatter="formatName" tree-node />
                        <vxe-column title="条件" class-name="conds" width="550">
                            <template #default="{ row }">
                                <bcomSortCond v-for="acond in row?.attr_conds" :cond="acond" />
                            </template>
                        </vxe-column>
                        <vxe-column title="配置" >
                            <template #default="{ row }">
                                <bComCalcFormula v-for="(cond,index) in row.calc_settings" :cond="cond" :hasNext="row?.calc_settings?.length>1 && index<(row?.calc_settings?.length-1)" />
                            </template>
                        </vxe-column>
                        <vxe-column field="remarks" title="描述" width="300"></vxe-column>
                        <vxe-column title="操作" width="160">
                            <template #default="{ row }">
                                <el-button type="success" text icon="Plus" v-if="row.level!=1"
                                    @click.stop="showEditor($event.target,{ bcom: row.bcom, attr_conds: row.attr_conds,classification:row.classification,
                                    remarks: null,calc_settings:row.calc_settings })"></el-button>
                                <el-button type="primary" text icon="Edit" v-if="!row.level" @click.stop="showEditor($event.target, row)" :ref="`edit-${row.id}`"></el-button>
                                <el-button type="danger" text icon="Close" v-if="!row.level" @click.stop="DeleteData(row.id)"></el-button>
                            </template>
                        </vxe-column>
                    </vxe-table>
                </el-col>
            </el-row>
        </el-col>
    </el-row>
    <el-popover width="1310px" title="计算公式" :visible="rowEditerShow" placement="left" :virtual-ref="editRef" virtual-triggering>
        <BComCalcFormulaEditor ref="bcomEditor" :saveData="saveData" :editCancel="() => rowEditerShow = false" />
    </el-popover>
    
</template>

<script>
/**
 * 导入所需的依赖和组件
 */
import { ref, computed } from 'vue'

import { cloneDeep } from 'lodash-es'
import { VxeTable,VxeToolbar } from 'vxe-table';
import { GetBComAttributes } from '@/api/bComCalcAttribute'
import { QueryByClassifications, SaveAsync, DeleteAsync } from '@/api/bidCalcBComFormula'
import bcomSortCondEditor from '@/calcRule/BComSortCondEditor.vue'
import bcomSortCond from '@/calcRule/BComSortCond.vue'
import { ElMessage, ElMessageBox } from 'element-plus';

import BComCalcFormulaEditor from '@/calcRule/BComCalcFormulaEditor.vue'
import { GetBidCalcBCom } from '@/api/bidCalcBCom'
import bComCalcFormula from '@/calcRule/BComCalcFormula.vue'

import { allAreas,newUUID } from '@/calcRule/calc_config'

import { GetBidCalcFormula } from '@/api/bidCalcFormula'
import bComCalcFormulaTable from '@/calcRule/BComCalcFormulaTable.vue'


export default {
    /**
     * 注册使用的子组件
     */
    components: { bcomSortCondEditor, bcomSortCond, BComCalcFormulaEditor,bComCalcFormula ,bComCalcFormulaTable},
    
    /**
     * 组件初始化设置
     * @returns {Object} 返回组件初始化所需的数据
     */
    setup() {
        const dTable = ref<VxeTable>({});
        const classificationProps = { id: 'id', label: 'label', children: 'children', isLeaf: 'isLeaf', value: 'id' };
        const cprops = { multiple: true, checkStrictly: true};
        const customConfig={storage:true};
        return { dTable,customConfig,classificationProps,cprops,allAreas }
    },

    /**
     * 组件数据定义
     * @returns {Object} 返回组件所需的响应式数据
     */
    data() {
        return {
            sortDataLoading: true,
            tableData: [],
            /**
             * 属性
             */
            bidAttributes: [],
            /**
            * 属性Map
            */
            bidAttributesMap: null,
            /**
            * 属性值Map
            */
            bidAttributesValuseMap: null,
            /**
             * 编辑弹层
             */
            rowEditerShow: false,
            /**
             * 虚拟触发编辑按钮
             */
            editRef: null,
            /**
             * 当前构件分类
             */
            currentClassification: [],
            /**
             * 构件分类
             */
            classifications: [],
            /**
             * 计算规则构件
             */
            calcBComs:[],
            /**
             * 计算规则构件Map
             */
            calcBComsMap:null,
            /**
             * 所有构件
             */
            bcomOptions:[],
            treeExpandKeys:[],
            /**
             * 计算公式弹出框
             */
            formulaTableShow:false,
            /**
             * 所有计算公式
             */
            allFormulas:[],
        }
    },
    async mounted() {
        await this.loadBComAttributes();
        await this.loadClassification();
        await this.loadCalcBCom();
        await this.loadAllCalcBCom();

        await this.loadSortData();
        await this.loadFormulas();
    },

    methods: {
        /**
         * 加载计算公式数据
         * 根据当前选中的构件分类获取相关的计算规则数据
         * 并初始化树形结构
         */
        async loadSortData() {
            this.sortDataLoading = true;
            let data = await QueryByClassifications(this.currentSeries,this.currentClassification);
            if(this.showCurrentArea){
                data=data.filter(d=>d.all_series?.includes(this.currentSeries));
            }
            this.initTreeNodes(data);
            this.tableData =data;
            await this.dTable.reloadData(this.tableData);
            this.sortDataLoading = false;
        },
        /**
         * 初始化树形节点数据
         * 处理构件分类和构件数据的父子关系
         * @param {Array} data 需要初始化的数据数组
         */
        initTreeNodes(data){
            let tempBComs=[];
            data.forEach(d => {
                d.parentId=d.classification;
            });
            if(tempBComs.length>0)data.unshift(...tempBComs);
            let tempClassifications=this.classifications;
            if(this.currentClassification.length>0){
                tempClassifications=this.classifications.filter(c=>this.currentClassification.map(d=>d[0]).includes(c.id));
            }
            data.unshift(...tempClassifications.map(c=>{return {id:c.id.toString(),parentId:null,level:1,name:c.name}}));
        },
        /**
         * 加载构件属性数据
         * 获取所有构件属性并建立属性映射和属性值映射
         */
        async loadBComAttributes() {
            this.bidAttributes = await GetBComAttributes();
            this.bidAttributesMap = new Map(this.bidAttributes.map(d => [d.id, d.name]));
            this.bidAttributesValuseMap = new Map(this.bidAttributes.filter(d => d?.values?.length > 0).map(d => [d.id, d.values.split(',')]));
        },
        /**
         * 保存构件计算规则数据
         * 处理新增和编辑的数据保存
         * @param {Object} data 需要保存的数据对象
         */
        async saveData(data) {
            let tempData = cloneDeep(data);
            if (tempData?.bcom) {
                if (tempData?.id) {
                    let tempRow = this.tableData.find(d => d.id == tempData.id);
                    tempRow.bcom = tempData.bcom;
                    tempRow.attr_conds = tempData.attr_conds;
                    tempRow.remarks = tempData.remarks;
                    tempRow.calc_settings = tempData.calc_settings;
                }
                else {
                    tempData.id = newUUID();
                }
                let postData = {
                    id: tempData.id,
                    bcom: tempData.bcom,
                    attr_conds: tempData.attr_conds ? JSON.stringify(tempData.attr_conds) : '[]',
                    remarks: tempData.remarks,
                    calc_settings:tempData.calc_settings ? JSON.stringify(tempData.calc_settings) : '[]'
                };
                await SaveAsync(postData);
                await this.loadSortData();
                this.rowEditerShow = false;
                await this.loadSortData();
                this.dTable.scrollToRow(this.dTable.getRowById(postData.id));
                ElMessage({ type: 'success', message: '保存成功!' });
            }
        },
        /**
         * 删除构件计算规则数据
         * @param {String} id 需要删除的数据ID
         */
        async DeleteData(id) {
            ElMessageBox.confirm('确认删除?', 'Warning', {
                confirmButtonText: '确认',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(async () => {
                await DeleteAsync({ val: id });
                this.loadSortData();
                ElMessage({ type: 'success', message: '删除成功!' });
            })
            .catch(() => { });
        },
        /**
         * 显示编辑弹层
         * @param {Object} btn 触发按钮元素
         * @param {Object} row 当前行数据
         */
        showEditor(btn, row) {
            this.editRef = btn;
            this.rowEditerShow = true;
            this.$refs.bcomEditor.changeData(cloneDeep(row));
        },
        /**
         * 加载构件分类数据
         * 获取所有构件分类信息
         */
        async loadClassification() {
            this.classifications = await GetBidCalcBCom(true);
            this.loadAllCalcBCom();
        },
        /**
         * 加载构件数据
         * 获取所有构件信息并建立构件映射
         */
        async loadCalcBCom() {
            this.calcBComs = await GetBidCalcBCom();
            this.calcBComsMap = new Map(this.calcBComs.map(d => [d.id, d.alias]));
            this.loadAllCalcBCom();
        },
        /**
         * 加载所有构件数据
         * 组织构件分类和构件的树形结构数据
         */
        async loadAllCalcBCom() {
            this.bcomOptions = this.classifications.map(c=>{ return {
                    value:c.id,
                    label:c.name,
                    children: this.calcBComs.filter(d=>d.classification_id==c.id).map(d=>{return {value:d.id,label:d.alias,isLeaf:true }})
                }
            });
        },
        /**
         * 合并表格单元格
         * 处理表格中需要合并的单元格
         * @param {Object} params 包含行、列等信息的参数对象
         * @returns {Object} 返回合并单元格的配置
         */
        mergeRowColumnMethod({ row, $rowIndex, _columnIndex, column, visibleData }) {
            let colIndexs = [0];
            if (colIndexs.includes(_columnIndex)&&(row.level==undefined||row.level==null)) {
                let tempIndex= visibleData.findIndex(d=>d.id==row.id);
                let prevRow = visibleData[tempIndex - 1];
                if (prevRow && !prevRow.level && prevRow[column.field] === row[column.field]) {
                    return { rowspan: 0, colspan: 0 }
                } 
                else {
                    let countRowspan = 0;
                    let tempColumns=this.dTable.getColumns();
                    for (let index = tempIndex; index < visibleData.length; index++) {
                        let data = visibleData[index];
                        let isSame = false;
                        let cols= colIndexs.filter(c=>c<=_columnIndex);
                        for (let cIndex = 0; cIndex < cols.length; cIndex++) {
                            let tempField=tempColumns[cols[cIndex]].field;
                            isSame = data[tempField] === row[tempField];
                            if(!isSame)break;
                        }
                        if(isSame){
                            countRowspan++;
                        }
                        else break;
                    }
                    if (countRowspan > 1) {
                        return { rowspan: countRowspan, colspan: 1 };
                    }
                }
            }
        },
        /**
         * 格式化分类显示
         * @param {Object} params 包含行数据的参数对象
         * @returns {String} 返回格式化后的分类名称
         */
        formatClassification({row}){
            return this.classifications.find(d=>d.id==row.classification)?.name;
        },
        /**
         * 格式化构件显示
         * @param {String} bcom 构件ID字符串
         * @returns {Array} 返回构件名称数组
         */
        formatBCom(bcom){
            let temp_bcoms=bcom?.split(',');
            if(temp_bcoms?.length>0){
                let temp_name= this.calcBComs.filter(c=>temp_bcoms.includes(c.id.toString()))?.map(c=>c.alias);
                return temp_name;
            }
        },
        /**
         * 格式化名称显示
         * @param {Object} params 包含行数据的参数对象
         * @returns {String} 返回格式化后的名称
         */
        formatName({row}){
            return row?.name??this.formatBCom(row.bcom);
        },
        /**
         * 记录树形结构展开状态
         * @param {Object} params 包含展开状态和行数据的参数对象
         */
        treeExpand({expanded,row}){
            if(expanded){
                this.treeExpandKeys.push(row.id);
            }
            else{
               let keyIndex= this.treeExpandKeys.indexOf(row.id);
               if(keyIndex>-1){
                this.treeExpandKeys.splice(keyIndex,1);
               }
            }
            console.log(this.treeExpandKeys);
        },
        /**
         * 处理单元格双击事件
         * 根据不同情况触发编辑或展开/收起树节点
         * @param {Object} params 包含行数据和列索引的参数对象
         */
        cellDBClick({ row,columnIndex }){
            if(!row.level&&columnIndex!=4)this.$refs[`edit-${row.id}`].$el.click();
            else 
            {
                this.dTable.toggleTreeExpand(row);
                this.treeExpand({expanded: this.dTable.isTreeExpandByRow(row),row});
            }
        },
        /**
         * 加载计算公式数据
         * 获取所有计算公式信息
         */
        async loadFormulas() {
            this.allFormulas = await GetBidCalcFormula(2);
        },
    },
    /**
     * 提供给子组件的数据
     * @returns {Object} 返回提供给子组件的计算属性
     */
    provide() {
        return {
            bidAttributes: computed(() => this.bidAttributes),
            bidAttributesMap: computed(() => this.bidAttributesMap),
            bidAttributesValuseMap: computed(() => this.bidAttributesValuseMap),
            classifications: computed(() => this.classifications),
            calcBComs: computed(() => this.calcBComs),
            calcBComsMap:computed(() => this.calcBComsMap),
            bcomOptions: computed(() => this.bcomOptions),
            allFormulas:computed(() => this.allFormulas)
        };
    }
}
</script>

/**
 * 组件样式定义
 * 包含页面布局、表格样式、按钮样式等
 */
<style scoped>
.bcom-header {
    padding: 10px;
    background-color: var(--el-color-info-light-9);
    height: 50px;
    display: block;
}

.title {
    display: flex;
    justify-content: center;
    align-items: center;
}

.link {
    display: flex;
    justify-content: right;
}

.bcom-choose {
    justify-content: left;
    align-items: center;
    background-color: white;
    padding: 0 0 10px 10px;
}

.bcom-choose .el-col {
    display: flex;
}

.bcom-choose .el-text {
    margin-right: 6px;
}

.bcom-choose .el-radio {
    margin-top: 5px;
    margin-right: 15px;
}

.bcom-choose .el-radio-group {
    max-width: 100%;
}

.bcom-card .bcom-from {
    padding: 18px 0;
}

.vxe-cell .el-button+.el-button {
    margin-left: 1px;
}

.deduct+.deduct {
    margin-left: 10px;
}


.conds {
    display: inline-flex;
    /* display: flex ; */
    align-items: center;
}

:deep(.sort-table table){
    border-collapse:collapse;
}


:deep(.sort-table .row--current .vxe-cell) {
    height: auto !important;
    max-height: none !important;
}

:deep(.row--current .attrs .vxe-cell) {
    overflow: visible;
    display: flex;
    flex-wrap: wrap;
}

:deep(.attrs .vxe-cell .el-tag) {
    white-space: break-spaces;
    margin-top: 3px;
    margin-bottom: 3px;
}

:deep(.row--current .attrs .vxe-cell .el-tag) {
    height: auto;
    min-height: 24px;
}

:deep(.deduct) {
    margin: 2px 0 2px 0;
    display:  inline-flex ;
    align-items: center;
}

:deep(.deduct+.deduct) {
    margin-left: 10px;
}

:deep(.deduct .el-text +.el-text) {
    margin-left: 5px;
}

:deep(.deduct .el-tag +.el-text) {
    margin-left: 3px;
}

:deep(.deduct .el-text +.el-tag){
    margin-left: 3px;
}

.rank-cell {
    display: flex;
    align-items: center;
}

.rank-cell .i-rank {
    width: 1em;
    height: 1em;
    margin-right: 3px;
    cursor: grab;
}

:deep(.sort-table .vxe-body--row.row--current) {
    background-color: #91d5f5;
}

:deep(.sort-table .vxe-body--row.row--hover.row--current) {
    background-color: #8bd3f5;
}

.copy-area-prop .el-select {
    margin-left: 10px;
}

.copy-area-prop .el-button {
    margin-left: 10px;
}

.copy-area-prop .el-select+.el-text {
    margin-left: 10px;
}
:deep(.bcom-choose .el-cascader){
    width: 300px;
}
:deep(.checkbox-cell .vxe-cell){
    display: flex;
    justify-content: center;
}
.area-series-select{
    width:82%;
    margin-right:5px;
}
:deep(.sort-table .el-button){
    height:24px;
    padding: 5px 9px ;
}
:deep(.sort-table .el-tag){
    height:20px;
}
:deep(.sort-table .area-tag){
    margin-bottom:2px;
}
:deep(.vxe-body--column){
    padding: 0 !important;
}
/* :deep(.vxe-body--expanded-row){
    background-color:#eee;
} */
</style>