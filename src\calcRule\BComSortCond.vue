<template>
    <el-text size="small"  type="warning" v-if="cond?.or?.length>0 || cond?.child?.length>0">{{ cond?.or?.length>0?'[':'{' }}</el-text>
    <el-text type="warning" v-if="isOrCond"> 或 </el-text>
    <el-text type="warning" v-if="isChildCond"> 且 </el-text>
    <el-text v-if="isChildCond&&pBCom" type="primary">{{ formatBComs(cond.bcom) }}</el-text>
    <!-- 属性 -->
    <el-tag v-if="cond.ctype=='1-2'" type="success" effect="light" disable-transitions>
        <el-text type="info" tag="b">{{ currentAttr }}</el-text>
        <el-text type="info" >{{ ` ${compateOptions.find(c=>c.value==cond.op)?.showLabel} ` }}</el-text>
        <el-text type="success" v-if="isMultiple(cond.op)" v-for="(cval,vindex) in cond.vals" >{{ cval }} <el-text  type="warning" v-if="vindex<cond.vals.length-1"> 或 </el-text></el-text>
        <el-text type="success" v-else >{{ cond.vals[0] }}</el-text>
    </el-tag>

    <!-- 范围 -->
    <el-tag v-if="cond.ctype=='1-3'" type="success" effect="light" disable-transitions>
        <el-text type="info">{{ `${currentAttr} 介于` }}</el-text><el-text type="success">{{ cond.sval }} ~ {{ cond.eval }}</el-text>
    </el-tag>

    <!-- 空间位置 -->
    <el-tag v-if="cond.ctype=='2-1'" effect="light" disable-transitions>
        <el-text v-if="(cond.position=='2-7' || cond.position=='2-8'|| cond.position=='2-9'|| cond.position=='2-10')&&cond.bcom" type="info">与 </el-text>
        <el-text v-else-if="cond.position!='2-6'&&cond.bcom" type="info">在 </el-text>
        
        <el-text v-if="cond.position!='2-6'" type="primary">{{ formatBComs(cond.bcom) }}</el-text>
        <el-text v-else type="success" >{{ positionOptions.find(c=>c.value==cond.position)?.showLabel }}</el-text>

        <el-text v-if="cond.position!='2-6'" type="success" >{{ positionOptions.find(c=>c.value==cond.position)?.showLabel }}</el-text>
        <el-text v-else type="primary">&nbsp;&nbsp;{{ formatBComs(cond.bcom) }}</el-text>
    </el-tag>

    <!-- 扣减部位 -->
    <el-tag v-if="cond.ctype=='3-1'" effect="light" disable-transitions>
        <el-text>扣减部位 是 </el-text>
        <el-text type="success">{{deductParts.find(d=>d.value==cond.deductpart).showLabel}}</el-text>
    </el-tag>

    <!-- 土方扣除条件 -->
    <el-tag v-if="cond.ctype=='4-1'" effect="light" disable-transitions>
    <el-text>土方扣除条件 是 </el-text>
    <el-text type="success">{{tfDeductLimit.find(d=>d.value==cond.calcgraphic).showLabel}}</el-text>
    </el-tag>

    <bcomSortCond v-if="cond?.or?.length>0" v-for="rc in cond.or" :cond="rc" :isOrCond="true" />
    <bcomSortCond v-if="cond?.child?.length>0" v-for="c in cond.child" :cond="c" :isChildCond="true" :pBCom="cond.bcom" />
    <el-text size="small" type="warning" v-if="cond?.or?.length>0 || cond?.child?.length>0">{{ cond?.or?.length>0?']':'}' }}</el-text>
    <!-- <el-divider direction="vertical" v-if="hasNext" /> -->
    <el-text type="warning" v-if="hasNext"> 且 </el-text>
</template>
<script>
import {compateOptions,positionOptions,isMultiple,deductParts,tfDeductLimit} from '@/calcRule/calc_config'


export default {
    inject:['bidAttributesMap','calcBComs'],
    name: 'bcomSortCond',
    props: {
        /**
         * 条件
         */
        cond: Object,
        /**
         * 是否为or条件
         */
        isOrCond:Boolean,
        /**
         * 是否子条件
         */
        isChildCond:Boolean,
        /**
         * 父级构件
         */
        pBCom:String,
        hasNext:Boolean
    },
    setup() {
        return {compateOptions,positionOptions,isMultiple,deductParts,tfDeductLimit}
    },
    data(){
        return{
            currentAttr:this.bidAttributesMap?.get(this.cond.attr)??this.cond.attr
        }
    },
    methods:{
        formatBComs(bcoms){
            if(bcoms)
            return  this.calcBComs.filter(c=> bcoms?.toString()?.split(',')?.includes(c.id.toString()))?.map(c=>c.alias)?.join(',');
        }
    }
}
</script>
<!-- <style scoped>
.vxe-cell .el-tag  {
    margin-left: 3px;
}

.tr-kh{
    margin-left: 3px;
    font-size: 22px;
    vertical-align: text-bottom;
}
</style> -->