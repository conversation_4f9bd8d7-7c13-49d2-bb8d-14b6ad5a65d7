<!-- <template>
    <vxe-toolbar>
        <template #buttons>
            <vxe-button icon="vxe-icon-square-plus" @click="newRow">新增</vxe-button>
            <vxe-button icon="vxe-icon-save" @click="saveTable" >保存</vxe-button>
        </template>
    </vxe-toolbar>
    <vxe-table ref="attrsTable" height="500" show-overflow keep-source 
        :edit-config="{ trigger: 'dblclick', showStatus: true, mode: 'cell' }" 
        :row-config="{ useKey: true, isHover: true,  keyField: 'id' }" :loading="tableLoading" :data="tbData">
        <vxe-column title="#" width="60" type="seq" />
        <vxe-column title="名称" width="200" field="name" align="center" :edit-render="{  autofocus: '.vxe-input--inner' }">
            <template #edit="{ row }">
                <vxe-pulldown v-model="row.showPull" destroy-on-close >
                    <template #default>
                        <vxe-input v-model="row.name" @focus="row.showPull=!row.showPull;attributesLoad(row.name)" @change="(e)=>attributesLoad(e.value)" :onkeypress="keydownEnter" ></vxe-input>
                    </template>
                    <template #dropdown>
                        <vxe-list height="200" class="dropdown" :data="localAttributes">
                            <template #default="{ items }">
                                <div class="list-item" v-for="item in items" :key="item.name" @click="nameSelect(item,row)">
                                    <span>{{ item.name }}</span>
                                </div>
                            </template>
                        </vxe-list>
                    </template>
                </vxe-pulldown>
            </template>
        </vxe-column>
        <vxe-column title="属性值"  field="values" :edit-render="{ }">
            <template #default="{row}">
                <el-tag class="area-tag" v-if="row.values?.length>0" v-for="(val,index) in row.values?.split(',')" disable-transitions >{{ val }}</el-tag>
            </template>
            <template #edit="{row}">
                <vxe-input v-model="row.values" v-show="false" ></vxe-input>
                <el-tag class="area-tag" v-if="row.values?.length>0" v-for="(val,index) in row.values?.split(',')" disable-transitions :key="val" closable @close="valDel(index,row)"  >{{ val }}</el-tag>
                <el-input v-if="row.inputVisible" v-model="row.inputValue"  size="small" @keyup.enter="inputEnter(row)"  @blur="inputEnter(row)" style="width: 120px;" />
                <el-button v-else size="small" @click="row.inputVisible=true" style="margin-left: 5px;" >+ 添加新值</el-button>
            </template>
        </vxe-column>
        <vxe-column title="操作" width="70">
            <template #default="{row}">
                <vxe-button icon="vxe-icon-delete" @click="delRow(row)"></vxe-button>
            </template>
        </vxe-column>
    </vxe-table>
</template>

<script>
import { GetBComAttributes,SaveData,DeleteData } from '@/api/bComAttribute'
import {ref} from 'vue'
import { VXETable, VxeTable } from 'vxe-table';
export default {
    inject: ['attributes'],
    name: "bComAttributeTable",
    setup() {
        const attrsTable = ref<VxeTable>({});
        return { attrsTable  }
    },
    data() {
        return {
            tableLoading: true,
            atLoading: false,
            tbData: [],
            localAttributes: [],
        }
    },
    async mounted() {
        await this.LoadData();
    },
    methods: {
        lengtSort: (a, b) => a.name.length - b.name.length,
        /**
         * 加载计算类型
         */
        async LoadData() {
            this.tableLoading = true;
            this.tbData = await GetBComAttributes();
            this.tableLoading = false;
        },
        
        attributesLoad(keyword) {
            this.atLoading = true;
            if (keyword) {
                this.localAttributes = this.attributes.filter(c => c.name.toLowerCase().includes(keyword.toLowerCase())).sort(this.lengtSort);
            }
            else {
                this.localAttributes = [];
            }
            this.atLoading = false;
        },
        nameSelect(item,row){
            row.name=item.name;
            row.attr_id=item.id;
            delete row.showPull;
            this.attrsTable.clearEdit();
            // row.values=item.values.split('★').toString();
        },
        /**
         * enter键退出编辑
         * @param {object} e 
         */
         keydownEnter(e){
            if(e.code==='Enter') this.attrsTable.clearEdit();
        },
        async delRow(row){
            if (this.attrsTable.isInsertByRow(row)) {
                this.attrsTable.remove(row); 
                return;
            }
            let type=await VXETable.modal.confirm("您确定要删除该数据?","删除确认",{zIndex:9998});
            if(type==='confirm'){
                let result= await DeleteData(row.id);
                if(result){
                   
                    VXETable.modal.message({content:'删除成功!',status:'success',zIndex:9999});
                }
                else{
                    VXETable.modal.message({content:'删除失败!',status:'error',zIndex:9999});
                }
                this.LoadData();
            }
        },
        async newRow(){
            let temp_row=await this.attrsTable.insertAt({id:null,name:null});
            await this.attrsTable.setEditRow(temp_row.row);
        },
        async saveTable(){
            let tempArr= this.attrsTable.getUpdateRecords()??[];
            let inserts=this.attrsTable.getInsertRecords()??[];
            tempArr.push(...inserts.map(t=>{return {id:null,name:t.name,attr_id:t.attr_id}}));
            if(tempArr?.length>0){
               let result=  await SaveData(tempArr);
               if(result){
                VXETable.modal.message({content:'保存成功!',status:'success',zIndex:9999});
               }
               else{
                VXETable.modal.message({content:'保存失败!',status:'error',zIndex:9999});
               }
               this.LoadData();
            }
        },
        valDel(index,row){
            let tempArr=row.values.split(',');
            tempArr.splice(index,1);
            row.values=tempArr.toString();
        },
        inputEnter(row)
        {
            if(row.inputValue){
                let tempArr=row.values?.split(',')??[];
                tempArr.push(row.inputValue);
                row.values=tempArr.toString();
            }
            delete row.inputVisible;
            delete row.inputValue
        }
        
    }
}
</script>

<style scoped lang="scss">
.dropdown {
    border-radius: 4px;
    border: 1px solid #dcdfe6;
    background-color: #fff;
}

.list-item {
    line-height: 22px;
}

.list-item:hover {
    background-color: #f5f7fa;
}

</style> -->