import axios from "axios"

/**
 * 获取图元
 * @returns 
 */
export function GetGraphicElements(component,calc_component,condsStr){
    return axios.post(`/Calc/GetGraphicElements`,{component:component,calc_component:calc_component ,query_json:condsStr})
        .then((res) => {
            return res.data.data;
        })
}


/**
 * 获取楼层
 * @returns 
 */
 export function GetFloors(){
    return axios.get(`/Calc/GetFloors`)
        .then((res) => {
            return res.data.data;
        })
}


/**
 * 获取所有工程量
 * @returns 
 */
 export function GetAllQuantity(floor){
    return axios.get(`/Calc/GetAllQuantity?floor=${floor}`)
        .then((res) => {
            return res.data.data;
        })
}

/**
 * 获取工程量
 * @returns 
 */
 export function GetQuantity(com_id,units,calc_rule){
    return axios.post(`/Calc/GetQuantity`,{id:com_id,units:units,calc_rule:calc_rule})
        .then((res) => {
            return res.data.data;
        })
}


/**
 * 获取相交构件x3d
 * @returns 
 */
 export function GetIntersectsIds(com_id){
    return axios.get(`/Calc/GetIntersectsIds?com_id=${com_id}`)
        .then((res) => {
            return res.data.data;
        })
}

/**
 * 根据条件获取相交构件x3d
 * @returns 
 */
 export function GetIntersectsWithJsonPath(com_id,condsStr){
    return axios.post(`/Calc/GetIntersectsWithJsonPath`,{id:com_id,val:condsStr})
        .then((res) => {
            return res.data.data;
        })
}


/**
 * 图元属性
 * @returns 
 */
 export function GetAttrs(com_id){
    return axios.get(`/Calc/GetAttrs?com_id=${com_id}`)
        .then((res) => {
            return res.data.data;
        })
}

/**
 * 执行扣减
 * @returns 
 */
 export function ExecDeduct(com_id){
    return axios.post(`/Calc/ExecDeduct`,{id:com_id})
        .then((res) => {
            return res.data.data;
        })
}

/**
 * 获取扣减数据
 * @returns 
 */
 export function GetCalcDeductData(com_id){
    return axios.get(`/Calc/GetCalcDeductData?com_id=${com_id}`)
        .then((res) => {
            return res.data.data;
        })
}

/**
 * 获取扣减信息
 * @returns 
 */
 export function GetCalcDeductInfo(com_id){
    return axios.get(`/Calc/GetCalcDeductInfo?com_id=${com_id}`)
        .then((res) => {
            return res.data.data;
        })
}