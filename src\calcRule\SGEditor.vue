<template>
        <el-form label-width="100px" >
             <el-form-item label="配置" >
                <el-select v-model="currentRow.sg_key" placeholder="配置" style="width: 400px;" filterable disabled >
                    <el-option v-for="ck in sgConfigKeys" :key="ck.value" :label="ck.label" :value="ck.value" />
                </el-select>
            </el-form-item>
            <!-- <el-form-item label="地区" >
                <el-select v-model="currentAreas" persistent :teleported="false" placeholder="地区" multiple @change="(vals)=>currentRow.areas=(vals?.join(','))"  >
                    <el-option v-for="area in allAreas" :key="area" :value="area" :label="area" ></el-option>
                </el-select>
            </el-form-item> -->
             <el-form-item label="地区" >
                <el-text>{{ currentRow.area }}</el-text>
            </el-form-item>
            <el-form-item label="构件" v-if="currentRow.sg_key=='workspace_width'">
                <!-- <el-input v-model="currentRow.component" style="width: 600px;"></el-input> -->
                <el-cascader ref="bcomCascader" v-model="currentBCom" :props="cprops" placeholder="构件选择" :options="graphicalComponents"  style="width: 420px;"
                        filterable clearable collapse-tags collapse-tags-tooltip  @change="bcomChange" :teleported="false" />
            </el-form-item>
          
            <el-form-item label="条件" class="cond-tr" v-if="currentRow.sg_key=='workspace_width'" >
                <attrEditor v-for="attr of Object.keys(currentRow.conds)" :attrName="attr" :conds="currentRow.conds" :delCond="()=>delete currentRow.conds[attr]" :key="attr"  />
                <el-popover placement="right" trigger="click" >
                    <template #reference>
                        <el-button size="small" icon="plus" circle type="primary" class="btn-plus"></el-button>
                    </template>
                    <el-select size="small" @change="(op)=>addAttr(currentRow,'conds',op)" placeholder="选择条件" >
                        <el-option v-for="item in wkCondOptions" :key="item.value" :label="item.label" :value="item.value" />
                    </el-select>
                </el-popover>
            </el-form-item>

            <el-form-item label="配置值">
                <el-input v-model="currentRow.sg_value" style="width: 200px;"></el-input>
            </el-form-item>

            <el-form-item label="描述">
                <el-input v-model="currentRow.remarks" style="width: 600px;"></el-input>
            </el-form-item>

            <el-form-item>
                <el-button type="primary" @click="saveData(currentRow)">{{ this.currentRow.id ? '保存' : '新增' }}</el-button>
                <el-button @click="editCancel">取消</el-button>
            </el-form-item>
        </el-form>
</template>
<script>
import attrEditor from './AttrEditor.vue'
import {wkCondOptions,allAreas,sgConfigKeys} from '@/calcRule/calc_config'

export default {
    name: 'sgEditor',
    inject:['graphicalComponents'],
    components: { attrEditor},
    props: {
        saveData: Function,
        editCancel: Function,
        currentRow:Object
    },
    setup() {
        const cprops = { multiple: true, checkStrictly: true,label:'title',value:'id'};
        return { wkCondOptions,allAreas,sgConfigKeys,cprops }
    },
    data() {
        return {
            currentBCom:this.currentRow?.component?.split(',')??[]
            // currentAreas:this.currentRow.areas?.split(',')??[]
        }
    },
    methods: {
        /**
         * 添加属性
         */
        addAttr(obj,attr,attrName) {
            let attrType=wkCondOptions.find(d=>d.value==attrName)?.type;
            switch (attrType) {
                case 'u-switch':
                    obj[attr][attrName]=true;
                    break;
                case 'range':
                    obj[attr][attrName]=[0,500];
                    break;
                default:
                obj[attr][attrName]=null;
                    break;
            }
            // switch (attrName) {
            //     case 'kdgzz':
            //     case 'zdtb':
            //     case 'dsjsj':
            //     case 'bpzh':
            //         obj[attr][attrName]=true;
            //         break;
            //     case 'kd':
            //         obj[attr][attrName]=[0,500];
            //         break;
            //     default:
            //     obj[attr][attrName]=null;
            //         break;
            // }
        },
        bcomChange(val){
            this.currentRow.component=val?.flatMap(d=>d[1])?.join(',');
            let checked_nodes= this.$refs.bcomCascader.getCheckedNodes();
            this.currentRow.component_label=checked_nodes?.filter(n=>n.level==2)?.map(n=>n.text)?.join(',');
        }
    },
}
</script>
<style scoped>
.bcom-tabs{
    width: 100%;
}
.btn-plus{
    margin-left: 5px;
}
.bcom-tabs .el-form-item{
    margin-bottom: 10px;
}
.deductText{
    margin-left: 20px;
    margin-right: 10px;
}

.bcom-tabs .i-rank{
    width: 1em;
    height: 1em;
    margin-right: 3px;
    cursor:grab;
}

:deep(.bcom-tabs .el-tabs__new-tab){
    margin-right:12px;
}
:deep(.bcoms-cascader){
    width: 300px;
}
.cond-tr{
    padding:4px;
    border:1px dashed var(--el-border-color);
    border-radius: 4px;
}
</style>

