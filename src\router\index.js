import { createRouter, createWebHistory } from 'vue-router'
import bidComRelationNew from '../views/TextTools/BidComRelationNew.vue'
import bidComCbalcRule from '../views/TextTools/BidComCalcRule.vue'
import calcView from '../views/TextTools/CalcView.vue'
import bidCalcSGTable from '@/calcRule/BidCalcSGTable.vue' 
import bidCalcStepSlopeTable from '@/calcRule/BidCalcStepSlopeTable.vue' 
import bidCalcThicknessTable from '@/calcRule/BidCalcCalcThicknessTable.vue' 

import calcQuotaViewNew from '../views/TextTools/CalcQuotaViewNew.vue'
import calcQuotaViewNew2 from '../views/TextTools/CalcQuotaViewNew2.vue'
import bidComCalcFormula from '../views/TextTools/BidComCalcFormula.vue'
import CalcQuotaView3 from '../views/TextTools/CalcQuotaView3.vue'
import CalcQuotaViewNewMain from '../views/TextTools/CalcQuotaViewNewMain.vue'
import CalcQuotaViewNewProject from '../views/TextTools/CalcQuotaViewNewProjectDemo.vue'

const routes = [
  {
    path: '/',
    name: 'bidComRelationNew',
    component: bidComRelationNew,
    meta:{title:'构件扣减配置'}
  },
  {
    path: '/bidComCbalcRule',
    name: 'bidComCalcRule',
    component: bidComCbalcRule,
    meta:{title:'构件计算配置'}
  },
  {
    path: '/bidComCalcFormula',
    name: 'bidComCalcFormula',
    component: bidComCalcFormula,
    meta:{title:'构件计算公式配置'}
  },
  {
    path: '/calcView',
    name: 'calcView',
    component: calcView,
    meta:{title:'工程量计算'}
  },
  {
    path: '/sgconfig',
    name: 'sgconfig',
    component: bidCalcSGTable,
    meta:{title:'二次生成配置'}
  },
  {
    path: '/stepslopeconfig',
    name: 'stepslopeconfig',
    component: bidCalcStepSlopeTable,
    meta:{title:'放坡系数配置'}
  },
  {
    path: '/calcthicknessconfig',
    name: 'calcthicknessconfig',
    component: bidCalcThicknessTable,
    meta:{title:'砌体墙厚度配置'}
  },
  
  {
    path: '/calcQuotaViewNew',
    name: 'calcQuotaViewNew',
    component: calcQuotaViewNew,
    meta:{title:'指标检测'}
  },
  {
    path: '/calcQuotaViewNew2',
    name: 'calcQuotaViewNew2',
    component: calcQuotaViewNew2,
    meta:{title:'图框指标检测'}
  },
  {
    path: '/CalcQuotaView3',
    name: 'CalcQuotaView3',
    component: CalcQuotaView3,
    meta:{title:'指标检测'}
  },
  {
    path: '/CalcQuotaViewMain',
    name: 'CalcQuotaViewMain',
    component: CalcQuotaViewNewMain,
    meta:{title:'单项工程指标检测'}
  },
  {
    path: '/CalcQuotaViewProject',
    name: 'CalcQuotaViewProject',
    component: CalcQuotaViewNewProject,
    meta:{title:'项目指标检测'}
  }
  
]

const router = createRouter({
  history: createWebHistory(process.env.BASE_URL),
  routes
})

export default router
