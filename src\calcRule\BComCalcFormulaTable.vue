<template>
    <vxe-toolbar>
        <template #buttons>
            <vxe-button icon="vxe-icon-square-plus" @click="newRow">新增</vxe-button>
            <vxe-button icon="vxe-icon-save" @click="saveTable" >保存</vxe-button>
        </template>
    </vxe-toolbar>
    <vxe-table ref="attrsTable" height="500"  keep-source 
        :edit-config="{ trigger: 'dblclick', showStatus: true, mode: 'row' }" 
        :row-config="{ useKey: true, isHover: true,  keyField: 'id', transform: true ,height:'auto'  }" :loading="tableLoading" :data="tbData">
        <vxe-column title="#" width="60" type="seq" />
        <vxe-column title="名称" width="300" field="name" align="center" :edit-render="{ autofocus: '.vxe-input--inner' }">
            <template #edit="{ row }">
                <vxe-input v-model="row.name" :onkeypress="keydownEnter" ></vxe-input>
            </template>
        </vxe-column>
        <vxe-column title="公式内容" width="300" field="mathml" align="center" :edit-render="{ autofocus: '.vxe-input--inner' }">
            <template #edit="{ row }">
                <vxe-input v-model="row.mathml" :onkeypress="keydownEnter" ></vxe-input>
            </template>
        </vxe-column>
        <vxe-column title="公式参数" field="fparams" :edit-render="{ }">
            <template #default="{row}">
                <el-tag class="value-tag" v-if="row.fparams?.length>0" v-for="(val,index) in row.fparams?.split(',')" disable-transitions >{{ val }}</el-tag>
            </template>
            <template #edit="{row}">
                <vxe-input v-model="row.fparams" v-show="false" ></vxe-input>
                <el-tag class="value-tag" v-if="row.fparams?.length>0" v-for="(val,index) in row.fparams?.split(',')" disable-transitions :key="val" closable @close="valDel(index,row)"  >{{ val }}</el-tag>
                <el-input v-if="row.inputVisible" v-model="row.inputValue"  size="small" @keyup.enter="inputEnter(row)"  @blur="inputEnter(row)" style="width: 120px;" />
                <el-button v-else size="small" @click="row.inputVisible=true" style="margin-left: 5px;" >+ 添加新值</el-button>
            </template>
        </vxe-column>
        <vxe-column title="类型" align="center" :edit-render="{ autofocus: '.el-select'}" >
            <template #default="{row}">
                <el-text v-if="row.type">{{ row.type==1?'计算':'公式' }}</el-text>
            </template>
            <template #edit="{ row }">
                <el-text v-if="row.type">{{ row.type==1?'计算':'公式' }}</el-text>
            </template>
        </vxe-column>
        <vxe-column title="备注" field="remarks" align="center" :edit-render="{ autofocus: '.vxe-input--inner' }" >
            <template #edit="{ row }">
                <vxe-input v-model="row.remarks" :onkeypress="keydownEnter" ></vxe-input>
            </template>
        </vxe-column>
        <vxe-column title="操作" width="120">
            <template #default="{ row }">
                <vxe-button icon="vxe-icon-copy" size="small" @click="copyRow(row)"></vxe-button>
                <vxe-button icon="vxe-icon-delete" size="small" @click="delRow(row)"></vxe-button>
            </template>
        </vxe-column>
    </vxe-table>
</template>

<script>
import { GetBidCalcFormula,SaveData,DeleteData } from '@/api/bidCalcFormula'
import { ref } from 'vue'
import { VXETable, VxeTable } from 'vxe-table';
export default {
    name: "bComCalcFormulaTable",
    props: {
        ftype:Number
    },
    setup() {
        const attrsTable = ref<VxeTable>({});
        return { attrsTable }
    },
    data() {
        return {
            tableLoading: true,
            tbData: [],
        }
    },
    async mounted() {
        await this.LoadData();
    },
    methods: {
        /**
         * 加载构件类型
         */
        async LoadData() {
            this.tableLoading = true;
            this.tbData = await GetBidCalcFormula(this.ftype);
            this.tableLoading = false;
        },
        /**
         * enter键退出编辑
         * @param {object} e 
         */
         keydownEnter(e){
            if(e.code==='Enter') this.attrsTable.clearEdit();
        },
        async delRow(row){
            if (this.attrsTable.isInsertByRow(row)) {
                this.attrsTable.remove(row); 
                return;
            }
            let type=await VXETable.modal.confirm("您确定要删除该数据?","删除确认",{zIndex:9998});
            if(type==='confirm'){
                let result= await DeleteData(row.id);
                if(result){
                    VXETable.modal.message({content:'删除成功!',status:'success',zIndex:9999});
                }
                else{
                    VXETable.modal.message({content:'删除失败!',status:'error',zIndex:9999});
                }
                await this.LoadData();
            }
        },
        async newRow(){
            let temp_row=await this.attrsTable.insertAt({id:null,name:null,remarks:null,type:this.ftype});
            await this.attrsTable.setEditRow(temp_row.row);
        },
        async copyRow(row){
            let temp_row=await this.attrsTable.insertAt({id:null,name:row.name,mathml:row.mathml,fparams:row.fparams,remarks:row.remarks,type:this.ftype});
            await this.attrsTable.setEditRow(temp_row.row);
        },
        async saveTable(){
            let tempArr= this.attrsTable.getUpdateRecords()??[];
            let inserts=this.attrsTable.getInsertRecords()??[];
            tempArr.push(...inserts.map(t=>{return {id:null,name:t.name,mathml:t.mathml,fparams:t.fparams,remarks:t.remarks,type:this.ftype}}));
            if(tempArr?.length>0){
               let result=  await SaveData(tempArr);
               if(result){
                VXETable.modal.message({content:'保存成功!',status:'success',zIndex:9999});
               }
               else{
                VXETable.modal.message({content:'保存失败!',status:'error',zIndex:9999});
               }
               await this.LoadData();
            }
        },
        valDel(index,row){
            let tempArr=row.fparams.split(',');
            tempArr.splice(index,1);
            row.fparams=tempArr.toString();
        },
        inputEnter(row)
        {
            if(row.inputValue){
                let tempArr=row.fparams?.split(',')??[];
                tempArr.push(row.inputValue);
                row.fparams=tempArr.toString();
            }
            delete row.inputVisible;
            delete row.inputValue
        }
    }
}
</script>

<style scoped lang="scss">
.dropdown {
    border-radius: 4px;
    border: 1px solid #dcdfe6;
    background-color: #fff;
}

.list-item {
    line-height: 22px;
}

.list-item:hover {
    background-color: #f5f7fa;
}
.value-tag{
    margin-top:3px ;
    margin-bottom: 3px;
}
.value-tag +.value-tag{
    margin-left: 4px;
}
.value-tag +.el-input{
    margin-left: 4px;
}
.value-tag +.el-button{
    margin-left: 4px;
}
</style>