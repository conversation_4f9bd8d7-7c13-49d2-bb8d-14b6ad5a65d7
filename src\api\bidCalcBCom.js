import axios from "axios"

/**
 * 获取计算规则构件
 * @returns 
 */
export function GetBidCalcBCom(isParent){
    return axios.get(`/BidCalcBCom/GetBidCalcBCom`+(isParent?`?isParent=${isParent}`:''))
        .then((res) => {
            let temp_data= res.data.data;
            temp_data.forEach(d => {
                d.conds=JSON.parse(d.conds);
            });
            return temp_data;
        })
}

/**
 * 保存构件分类
 * @param {Object} data 
 * @returns 
 */
export function SaveData(data){
    return axios.post(`/BidCalcBCom/SaveBidCalcBCom`, data)
    .then((res) => {
        return  res?.data?.succeeded;
    })
}

/**
 * 删除构件分类
 * @param {Number} id
 * @returns 
 */
export function DeleteData(id){
    return axios.post(`/BidCalcBCom/DelBidCalcBCom`, {id:id})
    .then((res) => {
        return res?.data?.succeeded;
    })
}

/**
 * 更新排序
 * @param {Array} ids
 * @returns 
 */
 export function UpdateSerial(ids){
    return axios.post(`/BidCalcBCom/UpdateSerial`, {items:ids})
    .then((res) => {
        return res?.data?.succeeded;
    })
}