<template>
    <vxe-toolbar>
        <template #buttons>
            <vxe-button icon="vxe-icon-square-plus" @click="newRow">新增</vxe-button>
            <vxe-button icon="vxe-icon-save" @click="saveTable" >保存</vxe-button>
            <vxe-button icon="vxe-icon-swap-up"  @click="changeSerial(true)" :disabled="!canSortToTop" />
            <vxe-button icon="vxe-icon-swap-down" @click="changeSerial(false)" :disabled="!canSortToBottom" />
        </template>
    </vxe-toolbar>
    <vxe-table ref="attrsTable" height="600"  keep-source 
        :edit-config="{ trigger: 'dblclick', showStatus: true, mode: 'row', }" 
        :row-config="{ useKey: true, isHover: true, isCurrent: true, keyField: 'id', transform: true, height: '28' }"
        @current-change="currentChange"
        :loading="tableLoading" :data="tbData">
        <vxe-column title="#" width="60" type="seq" />
        <vxe-column title="名称" width="300" field="name" align="center" :edit-render="{ autofocus: '.vxe-input--inner' }">
            <template #edit="{ row }">
                <vxe-input v-model="row.name" :onkeypress="keydownEnter" ></vxe-input>
            </template>
        </vxe-column>
        <vxe-column title="备注" field="remarks" align="center" :edit-render="{ autofocus: '.vxe-input--inner' }" >
            <template #edit="{ row }">
                <vxe-input v-model="row.remarks" :onkeypress="keydownEnter" ></vxe-input>
            </template>
        </vxe-column>
        <vxe-column title="操作" width="70">
            <template #default="{ row }">
                <vxe-button icon="vxe-icon-delete" @click="delRow(row)"></vxe-button>
            </template>
        </vxe-column>
    </vxe-table>
</template>

<script>
// import { GetBidCalcClassification,SaveData,DeleteData } from '@/api/bidCalcClassification'
import { GetBidCalcBCom,SaveData,DeleteData,UpdateSerial } from '@/api/bidCalcBCom'
import { ref } from 'vue'
import { VXETable, VxeTable } from 'vxe-table';
import { ElMessage } from 'element-plus';
export default {
    name: "bComCalcClassificationTable",
    setup() {
        const attrsTable = ref<VxeTable>({});
        return { attrsTable  }
    },
    data() {
        return {
            tableLoading: true,
            tbData: [],
            /**
             * 能否上移
             */
             canSortToTop: false,
            /**
             * 能否下移
             */
            canSortToBottom: false,
        }
    },
    async mounted() {
        await this.loadData();
    },
    methods: {
        /**
         * 加载构件类型
         */
        async loadData() {
            this.tableLoading = true;
            this.tbData = await GetBidCalcBCom(true);
            await this.attrsTable.reloadData(this.tbData);
            this.tableLoading = false;
        },
        /**
         * enter键退出编辑
         * @param {object} e 
         */
         keydownEnter(e){
            if(e.code==='Enter') this.attrsTable.clearEdit();
        },
        async delRow(row){
            if (this.attrsTable.isInsertByRow(row)) {
                this.attrsTable.remove(row); 
                return;
            }
            let type=await VXETable.modal.confirm("您确定要删除该数据?","删除确认",{zIndex:9998});
            if(type==='confirm'){
                let result= await DeleteData(row.id);
                if(result){
                    VXETable.modal.message({content:'删除成功!',status:'success',zIndex:9999});
                }
                else{
                    VXETable.modal.message({content:'删除失败!',status:'error',zIndex:9999});
                }
                await this.loadData();
            }
        },
        async newRow(){
            let temp_row=(await this.attrsTable.insertAt({id:null,name:null,remarks:null,serial:999},-1)).row;
            await this.attrsTable.scrollToRow(temp_row);
            await this.attrsTable.setEditRow(temp_row);
            temp_row.serial=(await this.attrsTable.getRowSeq(temp_row))-1;
        },
        async saveTable(){
            let tempArr= this.attrsTable.getUpdateRecords()??[];
            let inserts=this.attrsTable.getInsertRecords()??[];
            tempArr.push(...inserts.map(t=>{return {id:null,classification_id:0,name:t.name,remarks:t.remarks}}));
            if(tempArr?.length>0){
               let result=  await SaveData(tempArr);
               if(result){
                VXETable.modal.message({content:'保存成功!',status:'success',zIndex:9999});
               }
               else{
                VXETable.modal.message({content:'保存失败!',status:'error',zIndex:9999});
               }
               await this.loadData();
               if(inserts.length>0)this.attrsTable.scrollTo(0,9999);
            }
        },
        /**
         * 修改选中行
         */
        currentChange({ row,$rowIndex }) {
            if(this.attrsTable.isInsertByRow(row)){
                this.canSortToTop=false;
                this.canSortToBottom=false;
            }
            else{
                this.canSortToTop=$rowIndex!=0;
                this.canSortToBottom=($rowIndex!=this.tbData.length-1);
            }
        }, 
        /**
         * 修改排序
         * @param {Boolean} moveUp
         */
         async changeSerial(moveUp) {
            let tempRow = this.attrsTable.getCurrentRecord();
            let idArrray = this.tbData.map(d=>d.id);
            let currentIndex = idArrray.findIndex(d=>d==tempRow.id);
            let newIndex = currentIndex+(moveUp?-1:1);

            idArrray.splice(newIndex, 0, idArrray.splice(currentIndex, 1)[0]);

            if (idArrray?.length > 0) await UpdateSerial(idArrray);
            await this.loadData();

            this.attrsTable.scrollToRow(tempRow);
            this.attrsTable.setCurrentRow(this.attrsTable.getRowById(tempRow.id));
           
            ElMessage({ type: 'success', message: '排序完成!' });

        },
    }
}
</script>

<style scoped lang="scss">
.dropdown {
    border-radius: 4px;
    border: 1px solid #dcdfe6;
    background-color: #fff;
}

.list-item {
    line-height: 22px;
}

.list-item:hover {
    background-color: #f5f7fa;
}
.value-tag{
    margin-top:3px ;
    margin-bottom: 3px;
}
.value-tag +.value-tag{
    margin-left: 4px;
}
.value-tag +.el-input{
    margin-left: 4px;
}
.value-tag +.el-button{
    margin-left: 4px;
}
</style>