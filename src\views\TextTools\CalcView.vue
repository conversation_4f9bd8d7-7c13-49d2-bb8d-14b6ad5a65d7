<template>
    <el-row style="height:100%">
        <el-col :span="24">
            <el-affix :offset="0">
                <el-row class="bcom-header">
                    <el-row class="title">
                        <el-text size="large" tag="b">图元计算</el-text>
                    </el-row>
                </el-row>
            </el-affix>
            <el-row>
                <el-col :span="8">
                    <vxe-toolbar>
                        <template #buttons>
                            <el-text>构件 </el-text>
                            <el-select v-model="currentCom"  placeholder="构件" clearable  >
                                <el-option key="柱" label="柱" value="柱" />
                                <el-option key="梁" label="梁" value="梁" />
                                <el-option key="板" label="板" value="板" />
                                <el-option key="基础" label="基础" value="基础" />
                            </el-select>
                            <el-text>构件名称 </el-text>
                            <el-input  placeholder="构件名称" v-model="currentComponentName" style="width: 120px;"></el-input>
                            <el-text>条件 </el-text>
                            <calcCondEditor v-for="(acond, aindex)  in currentObj.attr_conds" :cond="acond" :delCond="()=>delCond(currentObj.attr_conds,aindex)"  />
                            <el-popover placement="right" trigger="click" >
                                <template #reference>
                                    <el-button size="small" icon="plus" circle type="primary" class="btn-plus"></el-button>
                                </template>
                                <el-select size="small" @change="(op)=>addCond(currentObj,'attr_conds',op)" placeholder="选择条件" >
                                    <el-option v-for="item in condOptions" :key="item.value" :label="item.label" :value="item.value" />
                                </el-select>
                            </el-popover>
                            <el-button text type="primary" icon="Search" @click="loadData" >查询</el-button>
                        </template>
                      
                    </vxe-toolbar>
                    <vxe-table ref="dTable" :data="showData" show-overflow stripe
                    :scroll-y="{enabled:true,gt:20}"
                    border height="900" :column-config="{ resizable: true }"
                    :row-config="{ useKey: true, isHover: true, isCurrent: true, keyField: '图元_id', transform: true, height: '28' }">
                        <vxe-column title="楼层" width="80" align="center" :formatter="formatFloor" :filters="floorsFilters" :filter-method="floorsFilte" />
                        <vxe-column field="构件名称" title="构件" width="110"  :filters="allComponentsFilters"></vxe-column>
                        <vxe-column field="下级词" title="下级词" width="100"></vxe-column>
                        <vxe-column field="component_name" title="构件名称" width="120"></vxe-column>
                        <vxe-column field="图元_id" title="图元_id" width="80"></vxe-column>
                        <vxe-column field="units" title="单位" width="80"></vxe-column>
                        <vxe-column field="attribute_code" title="属性编码" width="80"></vxe-column>
                        <vxe-column title="操作" width="80">
                            <template #default="{ row }">
                                <el-button type="success" text icon="View" @click.stop="viewData(row)"></el-button>
                            </template>
                        </vxe-column>
                    </vxe-table>
                </el-col>
                <el-col :span="16">
                    <el-row>
                        <el-form label-width="100px" v-if="currentRow" style="width: 100%" >
                            <el-row >
                                <el-col :span="4">
                                    <el-form-item label="构件:">
                                        <el-text>{{ currentRow?.构件名称 }}</el-text>
                                    </el-form-item>
                                </el-col>
                                <el-col :span="4" >
                                    <el-form-item label="下级词:">
                                        <el-text>{{ currentRow?.下级词 }}</el-text>
                                    </el-form-item>
                                </el-col>
                                <el-col :span="4">
                                    <el-form-item label="构件名称:">
                                        <el-text>{{ currentRow?.component_name }}</el-text>
                                    </el-form-item>
                                </el-col>
                                <el-col :span="4">
                                    <el-form-item label="图元id:">
                                        <el-text>{{ currentRow?.图元_id }}</el-text>
                                    </el-form-item>
                                </el-col>
                            </el-row>
                            <el-row>
                                <el-col :span="8">
                                    <el-form-item label="计算规则类型">
                                        <el-select v-model="calc_settings.cond_type" size="small" placeholder="计算规则类型" @change="condTypeChange" clearable>
                                            <el-option v-for="item in ruleTypes" :key="item.value" :label="item.label" :value="item.value" :disabled="item.value>2" />
                                        </el-select>
                                    </el-form-item>
                                </el-col>
                            </el-row>
                            <el-row v-if="calc_settings.cond_type==1">
                                <el-form-item label="计算系数" >
                                    <el-input size="small" placeholder="计算系数" v-model="calc_settings.factor" style="width: 300px;"></el-input>
                                </el-form-item>
                            </el-row>

                            <el-row v-if="calc_settings.cond_type==2">
                                <el-col :span="8">
                                    <el-form-item label="转换原属性">
                                        <el-select size="small" class="ew"  placeholder="属性" v-model="calc_settings.trans_attr" filterable  >
                                            <el-option v-for="attr in calcAttrs" :label="attr.label" :value="attr.value" />
                                        </el-select>
                                    </el-form-item>
                                </el-col>
                                <el-col :span="8">
                                    <el-form-item label="转换系数">
                                        <el-input size="small" placeholder="转换系数" v-model="calc_settings.factor" style="width: 200px;"></el-input>
                                    </el-form-item>
                                </el-col>
                            </el-row>

                            <!-- <el-row v-if="calc_settings.cond_type==3">
                                <el-col :span="8">
                                    <el-form-item label="延伸属性">
                                        <el-select size="small" class="ew"  placeholder="属性" v-model="calc_settings.extend_attr" filterable remote remote-show-suffix :remote-method="attributesLoad" :loading="atLoading" >
                                            <el-option v-for="attr in localAttributes" :label="attr.name" :value="attr.id" />
                                        </el-select>
                                    </el-form-item>
                                </el-col>
                                <el-col :span="8">
                                    <el-form-item label="延伸单位">
                                        <el-input size="small" placeholder="延伸单位" v-model="calc_settings.entended" style="width: 300px;"></el-input>
                                    </el-form-item>
                                </el-col>
                            </el-row>

                            <el-row v-if="calc_settings.cond_type==4">
                                <el-col :span="14">
                                    <vxe-table border size="mini" :data="tableData" :row-config="{height:28}" show-overflow >
                                        <vxe-column field="thickness" title="设计厚度" ></vxe-column>
                                        <vxe-column field="calc_thickness" title="计算厚度" ></vxe-column>
                                    </vxe-table>
                                </el-col>
                            </el-row> -->
                            <el-row>
                                <el-col :span="8">
                                    <el-form-item label="计量单位:">
                                        <el-select size="small" v-model="currentRow.units"  placeholder="计量单位" @change="currentRow.gcl=null">
                                            <el-option key="m3" label="m3" value="m3" ></el-option>
                                            <el-option key="m2" label="m2" value="m2" ></el-option>
                                            <el-option key="个" label="个" value="数量" ></el-option>
                                            <!-- <el-option key="长度" label="m(长度)" value="CD" ></el-option>
                                            <el-option key="厚度" label="m(厚度)" value="HD" ></el-option>
                                            <el-option key="宽度" label="m(宽度)" value="KD" ></el-option> -->
                                        </el-select>
                                    </el-form-item>
                                </el-col>
                                <el-col :span="8">
                                    <el-button type="primary" size="small"  @click="calc"  >计算</el-button>
                                </el-col>
                                <!-- <el-col :span="8">
                                    <el-button type="primary" size="small"  @click="viewIntersects(currentRow)">加载相交构件</el-button>
                                </el-col> -->
                            </el-row>

                            <el-row>
                                <el-col :span="8">
                                    <el-form-item label="工程量:">
                                        <el-text v-if="currentRow?.gcl" >{{ currentRow?.gcl }}</el-text>
                                        <el-text v-if="currentRow?.gcl" >{{ getUnitsLabel(currentRow.units) }}</el-text>
                                    </el-form-item>
                                </el-col>
                            </el-row>
                            <el-row>
                                <el-form-item label="条件">
                                    <calcCondEditor v-for="(acond, aindex)  in currentObj.r_attr_conds" :cond="acond" :delCond="()=>delCond(currentObj.r_attr_conds,aindex)"  />
                                        <el-popover placement="right" trigger="click" >
                                            <template #reference>
                                                <el-button size="small" icon="plus" circle type="primary" class="btn-plus"></el-button>
                                            </template>
                                            <el-select size="small" @change="(op)=>addCond(currentObj,'r_attr_conds',op)" placeholder="选择条件" >
                                                <el-option v-for="item in condOptions" :key="item.value" :label="item.label" :value="item.value" />
                                            </el-select>
                                        </el-popover>
                                        <el-button text type="primary" icon="View" @click="viewIntersectsWithJsonPath(currentRow)" >加载相交构件</el-button>
                                        <el-button text type="primary" icon="Scissor" @click="execDeduct(currentRow)" >扣减计算</el-button>
                                        <el-button text type="primary" icon="Finished" @click="viewDeductInfo(currentRow)" >扣减数据</el-button>

                                    </el-form-item>
                            </el-row>
                            <el-row v-if="currentAttrs">
                                <el-col :span="24">
                                    <el-form-item label="属性:"  >
                                        <div class="attrs">
                                            <el-text v-for="atKey of Object.keys(currentAttrs)" type="info" >{{ atKey }} : <el-text>{{ currentAttrs[atKey] }}</el-text></el-text>
                                        </div>
                                    </el-form-item>
                                </el-col>
                            </el-row>
                        </el-form>
                    </el-row>
                    <el-row>
                        <iframe ref="x3dViewer" width="100%" src="static/x3dview.html" height="700px" >
                        </iframe>
                    </el-row>
                </el-col>
            </el-row>
        </el-col>
    </el-row>
    <el-dialog v-if="deductInfoShow" v-model="deductInfoShow" title="扣减信息" width="90%" center align-center destory-on-close  >
        <el-form label-width="100px" style="width: 100%" >
            <el-row>
                <el-col :span="12">
                    <el-form-item label="工程量:">
                        <el-text v-if="currentRow?.gcl" >{{ currentRow?.gcl }}</el-text>
                        <el-text v-if="currentRow?.gcl" >{{ getUnitsLabel(currentRow.units) }}</el-text>
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item label="操作">
                        <el-button type="primary"  @click="viewDeductInfoX3D" >显示扣减图</el-button>
                        <el-button type="primary"  @click="viewDeductResultX3D" >显示扣减结果</el-button>
                    </el-form-item>
                </el-col>
            </el-row>

            <el-row>
                <el-col :span="24">
                <vxe-table ref="deductInfoTable" :data="deductInfo" show-overflow stripe
                    border height="230" :column-config="{ resizable: true }"
                    :row-config="{ useKey: true, isHover: true, isCurrent: true, keyField: 'id', transform: true, height: '28' }"
                    :checkbox-config="{checkField:'showX3D',visibleMethod:deductInfoCheckVisible,showHeader:false}"
                    @checkbox-change="viewDeductInfoX3D">
                        <vxe-column field="showX3D" width="60" type="checkbox" />
                        <!-- <vxe-column field="calc_component" title="当前构件" width="160"/> -->
                        <vxe-column field="target_calc_component" title="扣减构件" width="160"/>
                        <vxe-column field="deduct_operation" title="扣减类型" width="120" :formatter="formatOperation"  ></vxe-column>
                        <vxe-column title="扣减规则">
                            <template #default="{ row }">
                                <bcomDecuctCond v-for="(setting,index) in row?.deduct_rule?.deduct_settings" :row="row?.deduct_rule" :setting="setting" :key="setting"
                                 :hasNext="row?.deduct_rule?.deduct_settings?.length>1 && index<(row?.deduct_rule?.deduct_settings?.length-1)"  />
                            </template>
                        </vxe-column>
                        <vxe-column field="target_calc_component_ids" title="扣减图元id" width="300" :formatter="formatDeductIds" ></vxe-column>
                        <vxe-column field="attr_code" title="属性编码" width="80"></vxe-column>
                    </vxe-table>
                </el-col>
            </el-row>
            <el-row>
                <el-col :span="8">
                    <vxe-table ref="deductDataTable" :data="deductData" show-overflow stripe
                    border height="230" :column-config="{ resizable: true }"
                    :row-config="{ useKey: true, isHover: true, isCurrent: true, keyField: 'id', transform: true, height: '20' }"
                    @current-change="deductCurrentChange"
                    :checkbox-config="{checkField:'showX3D',visibleMethod:deductDataCheckVisible,showHeader:false}"
                    @checkbox-change="viewDeductInfoX3D"
                    >
                        <vxe-column field="showX3D" width="60" type="checkbox" />
                        <vxe-column field="target_id" title="扣除目标构件id" width="120"/>
                        <vxe-column title="计算规则构件" width="140" :formatter="formatDeductCalcComponentName"  ></vxe-column>
                        <vxe-column title="构件名称" width="80" :formatter="formatDeductComponentName"  ></vxe-column>
                        <vxe-column field="deduct_value" title="扣减量" width="200"  ></vxe-column>
                    </vxe-table>
                </el-col>
                <el-col :span="16">
                    <el-form-item label="属性:"  >
                        <div class="attrs">
                            <el-text v-for="atKey of Object.keys(currentCalcAttrs)" type="info" >{{ atKey }} : <el-text>{{ currentCalcAttrs[atKey] }}</el-text></el-text>
                        </div>
                </el-form-item>       
                </el-col>
              
            </el-row>
            <el-row>
                <el-col :span="24">
                    <iframe ref="x3dDeductViewer" width="100%" src="static/x3dview.html" height="600px" >
                    </iframe>
                </el-col>
            </el-row>
        </el-form>
    </el-dialog>
</template>

<script>
import { ref ,computed} from 'vue'
import { groupBy } from 'lodash-es'
import { VxeTable } from 'vxe-table'
import { GetAttrs,GetFloors,GetGraphicElements,GetIntersectsIds,GetIntersectsWithJsonPath,GetQuantity,ExecDeduct,GetCalcDeductData,GetCalcDeductInfo } from '@/api/calc'
import calcCondEditor from '@/calcRule/CalcCondEditor.vue'
import {condOptions,ruleTypes,addCond,delCond,calcAttrs} from '@/calcRule/calc_config'
import { GetGraphicalComponents } from '@/api/graphicalComponent'
import bcomDecuctCond from '@/calcRule/BComDeductCond.vue'
import { QueryByClassifications } from '@/api/bidCalcRelation'
import { ElMessage } from 'element-plus';
import { GetBComAttributes } from '@/api/bComCalcAttribute'
import { GetBidCalcBCom } from '@/api/bidCalcBCom'

export default {
    components:{calcCondEditor,bcomDecuctCond},
    setup() {
        const dTable = ref<VxeTable>({});
        const classificationProps = { id: 'id', label: 'label', children: 'children', isLeaf: 'isLeaf', value: 'id' };
        return { dTable, classificationProps,condOptions,addCond,delCond,ruleTypes,calcAttrs }
    },
    data() {
        return {
            tableData: [],
            showData:[],
            allComponents:[],
            floors:[],
            floorsFilters:[],
            allComponentsFilters:[],
            currentFloor:null,
            currentCom:null,
            currentObj:{attr_conds:[],r_attr_conds:[]},
            currentRow:null,
            x3dHtml:null,
            currentAttrs:null,
            graphicalComponents:[],
            calc_settings:{cond_type:null},
            deductInfoShow:false,
            deductData:[],
            deductInfo:[],
            deductRules:[],
            /**
             * 属性
             */
             bidAttributes: [],
            /**
            * 属性Map
            */
            bidAttributesMap: null,
            /**
            * 属性值Map
            */
            bidAttributesValuseMap: null,
             /**
             * 计算规则构件
             */
             calcBComs:[],
            /**
             * 计算规则构件Map
             */
            calcBComsMap:null,
            /**
             * 扣减目标构件属性
             */
            currentCalcAttrs:[],
            currentComponentName:null
        }
    },
    async mounted() {
        this.tableData= await GetGraphicElements();
        await this.loadFloors();
        await this.loadData();
        let temp_data =await GetGraphicalComponents();
        if(temp_data){
            this.graphicalComponents=temp_data;
        }
        this.deductRules= await QueryByClassifications(1,[]);
        await this.loadCalcBCom();
        await this.loadBComAttributes();
    },
    methods:{
        async loadData(){
            this.showData=await GetGraphicElements(this.currentCom,this.currentComponentName,JSON.stringify(this.currentObj.attr_conds));
            this.allComponentsFilters=Object.keys(groupBy(this.showData,'构件名称')).map(f=>{return {label:f,value:f}});
        },
        async loadFloors(){
            this.floors=await GetFloors();
            this.floorsFilters=this.floors.map(f=>{return {label:f.val,value:f.id}});
        },
        formatFloor({row}){
            return this.floors.find(f=>f.id==row.楼层_id)?.val;
        },
        floorsFilte({value,row}){
            return row.楼层_id==value;
        },
        /**
         * 计算工程量
         */
        async calc(){
            let data= await GetQuantity(this.currentRow.图元_id,this.currentRow.units,this.calc_settings?.cond_type?JSON.stringify(this.calc_settings):'{}');
            this.currentRow.gcl=data;
            this.x3dHtml=this.currentRow.x3d;
        },
        async viewData(row){
            this.currentRow=row;
            this.$refs.x3dViewer.contentWindow.changeScene([{color: this.getColor(row.构件名称),x3dStr:row.x3d}]);
            this.dTable.setCurrentRow(row);
            let temp_data=await GetAttrs(row.图元_id);
            this.currentAttrs= JSON.parse(temp_data);
        },
        getColor(name){
            let result='0 0 1';
            switch (name) {
                case '板':
                    result='1 0 0';
                    break;
                case '柱':
                    result='0 0 1';
                    break;
                case '墙':
                    result='220 150 0';
                    break;
                case '梁':
                    result='0 1 0';
                    break;
                default:
                    break;
            }
            return result;
        },
        getUnitsLabel(units){
            let result=units;
            switch (units) {
                case 'CH':
                case 'KD':
                case 'HD':
                    result='m';
                    break;
                case '数量':
                    result='个';
                    break;
                default:
                    break;
            }
            return result;
        },
        async viewIntersects(row){
           let result = await GetIntersectsIds(row.图元_id);
           let x3dIds=result.map(d=>d.id);
           if(x3dIds?.length>0){
            let x3dArray=this.tableData.filter(d=>x3dIds.includes(d.图元_id)).map(d=>{return {color:this.getColor(d.构件名称),x3dStr:d.x3d}});
            // x3dArray.push({color:this.getColor(row.构件名称),x3dStr:row.x3d});
            x3dArray.push({color:'222 0 99',x3dStr:row.x3d});
            this.$refs.x3dViewer.contentWindow.changeScene(x3dArray);
           }
        },
        async viewIntersectsWithJsonPath(row){
           let result = await GetIntersectsWithJsonPath(row.图元_id,JSON.stringify(this.currentObj.r_attr_conds));
           let x3dIds=result.map(d=>d.id);
           let x3dArray= [{color:'222 0 99',x3dStr:row.x3d}];
           if(x3dIds?.length>0){
            x3dArray.push(...this.tableData.filter(d=>x3dIds.includes(d.图元_id)).map(d=>{return {color:this.getColor(d.构件名称),transparency:'0.1',x3dStr:d.x3d}}));
           }
           this.$refs.x3dViewer.contentWindow.changeScene(x3dArray);
        },
        /**
         * 条件变更
         * @param {Number} val 
         */
         condTypeChange(val){
            let temp_attr=this.calc_settings?.attr;
            switch (val) {
                //计算系数
                case 1:
                    this.calc_settings={cond_type:val,attr:temp_attr,factor:null};
                    break;
                //属性转换
                case 2:
                    this.calc_settings={cond_type:val,attr:temp_attr,trans_attr:null,factor:null};
                    break;
                //延伸 加长
                case 3:
                    this.calc_settings={cond_type:val,attr:temp_attr,entended:null,entend_attr:null};
                    break;
                //砌体墙厚度计算表
                case 4:
                    this.calc_settings={cond_type:val,attr:temp_attr,thicknessTable:null};
                    // this.thicknessTableChange(1);
                    break;
                  //计算公式
                case 5:
                    this.calc_settings={cond_type:val,attr:temp_attr,formula:null,fparams:[]};
                    break;
                default:
                    break;
            }
        },
        formatOperation({row}){
            return row?.deduct_operation==1?'扣除':'不扣除';
        },
        formatDeductIds({row}){
           return row.target_calc_component_ids?.join();
        },
        /**
         * 运行计算
         * @param {Object} row 
         */
        async execDeduct(row){
           await ExecDeduct(row.图元_id);
           await ElMessage({ type: 'success', message: '计算完成!' });
        },
        /**
         * 显示扣减数据
         * @param {Object} row 
         */
        async viewDeductInfo(row){
            this.deductInfo=await GetCalcDeductInfo(row.图元_id);
            this.deductInfo?.forEach(d=>{
                d.deduct_rule=this.deductRules?.find(r=>r.id==d.rule_id);
                d.showX3D=true;
            });
            this.deductInfoShow=true;

            this.deductData=await GetCalcDeductData(row.图元_id);
            this.deductData.forEach(d=>d.showX3D=true);
        },
        /**
         * 显示扣减图形
         */
        async viewDeductInfoX3D(){
            let x3dArray= [{color:'0 1 0',x3dStr:this.currentRow.x3d}];
            this.deductInfo?.forEach(di=>{
                let tempArray=this.tableData.filter(d=>di.target_calc_component_ids?.includes(d.图元_id));
                if(di.deduct_operation==1){
                    tempArray=tempArray.filter(d=>this.deductData.some(dd=>dd.target_id==d.图元_id&&dd.showX3D==true));
                }
                else if(!di.showX3D){
                    tempArray=[];
                }
                tempArray=tempArray?.map(d=>{return {color:di.deduct_operation==1?'1 0 0':'0 0 1',transparency:di.deduct_operation==1?'0.2':'0.6',x3dStr:d.x3d}})
                x3dArray.push(...tempArray);
            });
            this.$refs.x3dDeductViewer.contentWindow.changeScene(x3dArray);


            // this.deductInfo?.forEach(di=>{
            //     let tempArray=this.tableData.filter(d=>di.target_calc_component_ids?.includes(d.图元_id))
            //     .map(d=>{return {color:di.deduct_operation==1?'1 0 0':'0 0 1',transparency:di.deduct_operation==1?null:'0.4',x3dStr:d.x3d}})
            //     x3dArray.push(...tempArray);
            // });
            // this.$refs.x3dDeductViewer.contentWindow.changeScene(x3dArray);
        },
        /**
         * 显示扣减后图形
         */
        async viewDeductResultX3D(){
            let tempData= this.deductData.find(d=>d.target_id==null);
            if(tempData)
            {
                let x3dArray= [{color:'0 1 0',x3dStr:'<Shape>'+tempData.x3d+'</Shape>'}];
                this.$refs.x3dDeductViewer.contentWindow.changeScene(x3dArray);
            }
        },
         /**
         * 加载属性
         */
         async loadBComAttributes() {
            this.bidAttributes = await GetBComAttributes();
            this.bidAttributesMap = new Map(this.bidAttributes.map(d => [d.id, d.name]));
            this.bidAttributesValuseMap = new Map(this.bidAttributes.filter(d => d?.values?.length > 0).map(d => [d.id, d.values.split(',')]));
        },
        /**
         * 加载构件
        */
        async loadCalcBCom() {
            this.calcBComs = await GetBidCalcBCom();
            this.calcBComsMap = new Map(this.calcBComs.map(d => [d.id, d.alias]));
        },
        /**
         * 加载扣除构件属性
         * @param {Object} row 
         */
        async deductCurrentChange({row}){
            if(row.target_id){
                let temp_data=await GetAttrs(row.target_id);
                this.currentCalcAttrs= JSON.parse(temp_data);
            }
        },
        /**
         * 输出构件名称
         * @param {Object} row 
         */
        formatDeductComponentName({row}){
            let temp_row= this.tableData.find(d=>d.图元_id==row.target_id);
            return temp_row?.component_name;
        },
        /**
         * 输出计算规则构件名称
         * @param {Object} row 
         */
         formatDeductCalcComponentName({row}){
            let temp_row= this.tableData.find(d=>d.图元_id==row.target_id);
            return temp_row?.calc_component;
        },
        deductDataCheckVisible({row}){
           return row.target_id;
        },
        deductInfoCheckVisible({row}){
           return row.deduct_operation!=1;
        }
    },
    provide() {
        return {
            graphicalComponents: computed(() => this.graphicalComponents),
            // bidAttributes: computed(() => this.bidAttributes),
            bidAttributesMap: computed(() => this.bidAttributesMap),
            // bidAttributesValuseMap: computed(() => this.bidAttributesValuseMap),
            calcBComs: computed(() => this.calcBComs),
            calcBComsMap: computed(() => this.calcBComsMap)
        };
    }
}
</script>

<style scoped>
.bcom-header {
    padding: 10px;
    background-color: var(--el-color-info-light-9);
    height: 40px;
    display: block;
}

.title {
    display: flex;
    justify-content: center;
    align-items: center;
}

.bcom-choose {
    justify-content: left;
    align-items: center;
    background-color: white;
    padding: 0 0 10px 10px;
}

.bcom-choose .el-col {
    display: flex;
}

.bcom-choose .el-text {
    margin-right: 6px;
}
.el-text{
    margin-left :6px;
}
.attrs{
    display: grid;
    justify-items: left;
    grid-template-columns:50% 50%;
    height: 400px;
    overflow-y: scroll;
    width: 100%;
}

</style>