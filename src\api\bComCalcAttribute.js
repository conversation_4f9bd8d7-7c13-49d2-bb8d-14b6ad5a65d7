import axios from "axios"

/**
 * 获取构件属性
 * @returns 
 */
export function GetBComAttributes(){
    return axios.get(`/BidCalcBCom/GetBComCalcAttributes`)
        .then((res) => {
            return res.data.data;
        })
}

/**
 * 保存构件属性
 * @param {Object} data 
 * @returns 
 */
export function SaveData(data){
    return axios.post(`/BidCalcBCom/SaveBComCalcAttribute`, data)
    .then((res) => {
        return  res?.data?.succeeded;
    })
}

/**
 * 删除构件属性
 * @param {Number} id
 * @returns 
 */
export function DeleteData(id){
    return axios.post(`/BidCalcBCom/DelBComCalcAttribute`, {id:id})
    .then((res) => {
        return res?.data?.succeeded;
    })
}

