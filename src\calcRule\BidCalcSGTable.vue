<template>
    <el-row class="bcom-header">
        <el-row class="title">
            <el-text size="large" tag="b">二次生成配置</el-text>
        </el-row>
    </el-row>
    <vxe-toolbar>
        <template #buttons>
            <el-select v-model="currentKey" style="width:400px" placeholder="配置"  filterable  @change="loadData">
                <el-option v-for="ck in sgConfigKeys" :key="ck.value" :label="ck.label" :value="ck.value" />
            </el-select>
            <el-select v-model="currentArea"  placeholder="地区" filterable  @change="areaChange">
                <el-option v-for="area in allAreas" :key="area" :label="(configAreas?.includes(area)?area+'  &#10003;':area)" :value="area" />
            </el-select>
            <vxe-button icon="vxe-icon-square-plus" @click="showEditor({id:null,area:currentArea,sg_key:currentKey,component:null,remarks:null,conds:{}})">新增</vxe-button>
        </template>
    </vxe-toolbar>
    <vxe-table ref="attrsTable" height="900"  keep-source :scroll-y="{enabled:true,gt:20}"
        :row-config="{ useKey: true, isHover: true,  keyField: 'id', transform: true ,height:'auto'  }" :loading="tableLoading" :data="tbData"
        :edit-config="{ trigger: 'manual', mode: 'row',autoClear:false }" 
        @cell-dblclick="({ row,columnIndex })=>{if(columnIndex!=6)showEditor(row)}">
        <!-- <vxe-column title="#" width="60" type="seq" /> -->
        <vxe-column title="#" width="60" type="seq">
            <template #default="{row}">
                <div class="rank-cell">
                    <Rank class="i-rank" /> {{ attrsTable.getRowSeq(row) }}
                </div>
            </template>
        </vxe-column>
        <vxe-column title="配置" width="400" field="key" :formatter="keyFormat" ></vxe-column>
        <vxe-column title="构件" >
            <template #default="{row}">
                <el-tag class="area-tag" type="info" v-if="row.component_label?.length>0" v-for="label in row.component_label?.split(',')" disable-transitions >{{ label }}</el-tag>
            </template>
        </vxe-column>
        <vxe-column title="条件"  >
            <template #default="{row}">
                <attrText v-for="attr of Object.keys(row.conds)" :attrName="attr" :conds="row.conds" />
            </template>
        </vxe-column>
        <vxe-column title="配置值" width="120" field="sg_value" ></vxe-column>
        <vxe-column title="备注" width="200" field="remarks" ></vxe-column>
        <!-- <vxe-column title="地区" width="80" field="area" >
            <template #edit="{ row }">
                <vxe-input v-model="row.area" disabled ></vxe-input>
            </template>
        </vxe-column> -->
        <!-- <vxe-column title="关联地区" field="area" width="23%" :edit-render="{ autofocus: '.el-select' }">
            <template #default="{row}">
                <el-button text icon="Setting" type="primary" @click="showAreasEdit(row)" ></el-button>
                <el-tag class="area-tag" v-if="row.area?.length>0" v-for="area in row.area?.split(',')" disable-transitions >{{ area }}</el-tag>
            </template>
            <template #edit="{row}">
                <el-select size="small" v-model="row.temp_areas" placeholder="地区" clearable multiple filterable :reserve-keyword="false" :teleported="false" collapse-tags collapse-tags-tooltip :max-collapse-tags="2" 
                class="area-series-select" @change="row.area=row.temp_areas.join(',')" >
                    <el-option v-for="area in allAreas" :label="area" :value="area" />
                </el-select>
                <el-button type="success" text icon="Select" @click.stop="saveRow(row)"></el-button>
                <el-button type="danger" text icon="CloseBold" @click.stop="attrsTable.clearEdit()"  ></el-button>
            </template>
        </vxe-column> -->
        <vxe-column title="操作" width="190">
            <template #default="{row}">
                <vxe-button icon="vxe-icon-edit" @click="showEditor(row)"></vxe-button>
                <vxe-button icon="vxe-icon-copy" size="small" @click="copyRow(row)"></vxe-button>
                <vxe-button icon="vxe-icon-delete" @click="delRow(row)"></vxe-button>
            </template>
        </vxe-column>
    </vxe-table>
    <el-dialog  v-model="rowEditerShow" title="编辑" width="60%" center align-center append-to-body >
        <sgEditor :key="currentRow" :saveData="saveRow" :editCancel="() => rowEditerShow = false" :currentRow="currentRow" />
    </el-dialog>
</template>

<script>
import Sortable from 'sortablejs'
import { GetCalcSGConfig,SaveData,DeleteData,GetAreas,UpdateCalcSGSerial } from '@/api/bidCalcSGConfig'
import { GetGraphicalComponents } from '@/api/graphicalComponent'

import { ref,computed } from 'vue'
import { VXETable, VxeTable } from 'vxe-table';
import sgEditor from '@/calcRule/SGEditor.vue'
import { cloneDeep } from 'lodash-es'
import {  allAreas,sgConfigKeys  } from '@/calcRule/calc_config'
import attrText from './AttrText.vue'
import { ElMessage } from 'element-plus';

export default {
    name: "bidCalcSGTable",
    components:{sgEditor,attrText},
    setup() {
        const attrsTable = ref<VxeTable>({});
        return { attrsTable,allAreas,sgConfigKeys }
    },
    data() {
        return {
            tableLoading: true,
            tbData: [],
            rowEditerShow:false,
            currentRow:null,
            currentArea:'北京',
            currentKey:'workspace_width',
            graphicalComponents:null,
            currentBCom:null,
            configAreas:null
        }
    },
    async mounted() {
        let query_area=this.$route.query.area;
        if(query_area)this.currentArea=query_area;
        await this.loadData();
        this.configAreas=await GetAreas(this.currentKey);
        let temp_data =await GetGraphicalComponents();
        if(temp_data){
            this.graphicalComponents=temp_data;
        }
        this.rowDrop();        
    },
    methods: {
        /**
         * 配置key显示
         * @param {Object} row 
         */
        keyFormat:({row})=> sgConfigKeys.find(k=>k.value==row.sg_key).label,
        /**
         * 加载数据
         */
        async loadData() {
            this.tableLoading = true;
            this.tbData = await GetCalcSGConfig(this.currentKey,this.currentArea);
            this.tableLoading = false;
        },
        async copyRow(row){
            let temp_row={
                id:null,remarks:row.remarks,area:this.currentArea,
                sg_key:row.sg_key,sg_value:row.sg_value,
                component_label:row.component_label,component:row.component,conds:row.conds,serial:row.serial};
                this.currentRow=temp_row;
                this.rowEditerShow = true;
        },
        /**
         * 删除
         */
        async delRow(row){
            if (this.attrsTable.isInsertByRow(row)) {
                this.attrsTable.remove(row); 
                return;
            }
            let type=await VXETable.modal.confirm("您确定要删除该数据?","删除确认",{zIndex:9998});
            if(type==='confirm'){
                let result= await DeleteData(row.id);
                if(result){
                   
                    VXETable.modal.message({content:'删除成功!',status:'success',zIndex:9999});
                }
                else{
                    VXETable.modal.message({content:'删除失败!',status:'error',zIndex:9999});
                }
                await this.loadData();
            }
        },
        async saveRow(data){
            let postData={id:data.id,area:data.area,remarks:data.remarks,component:data.component,component_label:data.component_label,
                sg_key:data.sg_key,sg_value:data.sg_value,conds:JSON.stringify(data.conds),serial:data.serial};
            await SaveData([postData]);
            VXETable.modal.message({content:'保存成功!',status:'success',zIndex:9999});
            await this.loadData();
            this.rowEditerShow=false;
        },
        /**
         * 显示行编辑弹框
         * @param {Object} row 行数据
         */
         showEditor(row) {
            this.currentRow=cloneDeep(row);
            this.rowEditerShow = true;
        },
        showAreasEdit(row){
            row.temp_areas=row.area?.length>0?row.area?.split(','):null;
            this.attrsTable.setEditRow(row)
        },
        async areaChange(val){
            this.tbData=await GetCalcSGConfig(this.currentKey,val);
            await this.attrsTable.reloadData(this.tbData);
        },
        /**
         * 拖动
         */
         async rowDrop() {
            this.sortable=Sortable.create(
                this.attrsTable.$el.querySelector('.body--wrapper>.vxe-table--body tbody'),
                {
                    handle:'.i-rank',//'.vxe-body--row',
                    animation:150,
                    onEnd:({newIndex,oldIndex})=>this.changeSerial(newIndex,oldIndex)
                }
            );
        },
        /**
         * 修改排序
         * @param {number} newIndex 
         * @param {number} oldIndex 
         */
        async changeSerial(newIndex,oldIndex){
            if(newIndex==oldIndex)return false;

            this.tbData.splice(newIndex,0,this.tbData.splice(oldIndex,1)[0]);
            this.attrsTable.reloadData(this.tbData);
            this.attrsTable.setCurrentRow(this.tbData[newIndex]);
            let ids=this.tbData.map(d=>d.id);
            await UpdateCalcSGSerial(ids);
            await this.loadData();
            ElMessage({ type: 'success', message: '排序完成!' });
        },
    },
    provide() {
        return {
            graphicalComponents: computed(() => this.graphicalComponents),
        };
    }

}
</script>

<style scoped lang="scss">
.bcom-header {
    padding: 10px;
    background-color: var(--el-color-info-light-9);
    height: 45px;
    display: block;
}

.title {
    display: flex;
    justify-content: center;
    align-items: center;
}
:deep(.vxe-buttons--wrapper .el-select){
    margin-right: 12px;
}
.area-series-select{
    width:84%;
    margin-right:5px;
}
:deep(.vxe-body--column .el-button){
    height:24px;
    padding: 5px 2px ;
}

:deep(.el-tag+.el-tag){
    margin-left: 4px;
}
.area-tag{
    margin-bottom: 3px;
}
.rank-cell{
    display: flex ;
    align-items: center;
}
.rank-cell .i-rank{
    width: 1em;
    height: 1em;
    margin-right: 3px;
    cursor:grab;
}
</style>