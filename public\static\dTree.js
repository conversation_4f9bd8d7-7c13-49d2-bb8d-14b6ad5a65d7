class dNode {
    constructor(id, pid, title, url) {
        this.id = id;
        this.pid = pid;
        this.title = title;
        this.url = url;
    }
}
class dTree {
    constructor() {
        this.nodes = [];
    }
    add(id, pid, title, url) {
        this.nodes.push(new dNode(id, pid, title, url));
    }
}

function ruleOnload() {
    var iframe=document.getElementById("ruleFrame").contentWindow;
    if(iframe){
        let headT = iframe.document.querySelector("table");
        if(headT){
            headT.remove();
        }
        let footP = iframe.document.querySelector("p.default.style3");
        if(footP){
            footP.remove();
        }
    }
}