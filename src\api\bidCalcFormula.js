import axios from "axios"

/**
 * 获取计算公式
 * @returns 
 */
export function GetBidCalcFormula(type){
    return axios.get(`/BidCalcBCom/GetCalcFormula?type=${type}`)
        .then((res) => {
            return res.data.data;
})
}

/**
 * 保存
 * @param {Object} data 
 * @returns 
 */
export function SaveData(data){
    return axios.post(`/BidCalcBCom/SaveCalcFormula`, data)
    .then((res) => {
        return  res?.data?.succeeded;
    })
}

/**
 * 删除
 * @param {Number} id
 * @returns 
 */
export function DeleteData(id){
    return axios.post(`/BidCalcBCom/DelCalcFormula`, {id:id})
    .then((res) => {
        return res?.data?.succeeded;
    })
}

