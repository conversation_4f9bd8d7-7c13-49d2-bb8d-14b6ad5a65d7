import axios from "axios"

/**
 * 获取所有单项工程信息
 * @returns 
 */
export function GetProjectBuilds(builddir) {
    return axios.post(`/projectquota/projectbuilds`, {builddir:builddir})
    .then((res) => {
        return res.data.data;
    })
}

/**
 * 获取用户
 * @returns 
 */
export function GetProjectUserInfo() {
    return axios.get(`/userprojectyresult/projectuserinfo`)
    .then((res) => {
        return res.data.data;
    })
}

/**
 * 获取项目列表(200条)
 * @returns 
 */
export function GetProjectResults(userid='',name='') {
    return axios.get(`/userprojectyresult/projectresults?userid=${userid}&name=${name}`)
    .then((res) => {
        return res.data.data;
    })
}

/**
 * 判断后基础信息表是否存在
 * @returns 
 */
 export function HoujExists(path) {
    return axios.get(`/userprojectyresult/houjexists?path=${path}`)
    .then((res) => {
        console.log(res);
        return res.data.data;
    })
}

/**
 * 构件指标验证(后基础信息表)
 * @returns 
 */
 export function HoujComponentQuota(userprojectid,buildingid,speciltyids,xgj_entnames,floornames,entobjdataids,hasHard=true) {
    return axios.post(`/projectquota/houjcomponentquota`,{userprojectid:userprojectid,buildingid:buildingid,speciltyids:speciltyids,xgj_entnames:xgj_entnames,floornames:floornames,entobjdataids:entobjdataids,hasHard:hasHard})
    .then((res) => {
        return res.data.data;
    }).catch((e)=>{
        return null;
    })
}

/**
 * 获取所有指标
 * @returns  
 */
 export function GetAllQuota() {
    return axios.get(`/userprojectyresult/getallquota`)
    .then((res) => {
        return res.data.data;
    })
}

