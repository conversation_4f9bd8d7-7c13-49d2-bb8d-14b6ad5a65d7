<template>
    <vxe-toolbar>
        <template #buttons>
            <el-select v-model="currentArea"  placeholder="地区" filterable clearable  @change="areaChange">
                <el-option v-for="area in allAreas" :key="area" :label="(configAreas?.includes(area)?area+'  &#10003;':area)" :value="area" />
            </el-select>
            <vxe-button icon="vxe-icon-square-plus" @click="newRow">新增</vxe-button>
        </template>
    </vxe-toolbar>
    <vxe-table ref="attrsTable" height="900"  keep-source :scroll-y="{enabled:true,gt:20}"
        :edit-config="{ trigger: 'dblclick',showStatus:true,  mode: 'row' ,autoClear:false}" 
        :row-config="{ useKey: true, isHover: true,  keyField: 'id', transform: true ,height:'auto'}" :loading="tableLoading" :data="tbData">
        <vxe-column title="#" width="60" type="seq" />
        <vxe-column title="地区" width="90" field="area" ></vxe-column>
        <vxe-column title="名称" width="300" field="name"  :edit-render="{ autofocus: '.vxe-input--inner'}" >
            <template #edit="{ row }">
                <vxe-input v-model="row.name" :onkeypress="keydownEnter" ></vxe-input>
            </template>
        </vxe-column>
        <vxe-column title="配置"  :edit-render="{ autofocus: '.vxe-input--inner' }" >
            <template #default="{row}">
                <vxe-table class="config-table" border size="mini" :data="row.trans_data" :row-config="{height:28}" show-overflow >
                    <vxe-column field="thickness" title="设计厚度" ></vxe-column>
                    <vxe-column field="calc_thickness" title="计算厚度" ></vxe-column>
                </vxe-table>
            </template>
            <template #edit="{ row }">
                <vxe-button class="config-add-btn" size="mini" icon="vxe-icon-square-plus" @click="row.trans_data.push({thickness:0,calc_thickness:0})">新增</vxe-button>
                <calcThicknessTable class="config-table" :key="row.trans_data" :sData="row.trans_data" :delData="(rowIndex)=>row.trans_data.splice(rowIndex,1)" />
                <!-- <vxe-table class="config-table"  border size="mini" :data="row.trans_data" :row-config="{height:28}" show-overflow :edit-config="{ trigger: 'click', mode: 'row' }" >
                    <vxe-column field="thickness" title="设计厚度" :edit-render="{ autofocus: '.vxe-input--inner'}" >
                        <template #edit="{ row }">
                            <vxe-input size="mini" type="number" v-model="row.thickness" ></vxe-input>
                        </template>
                    </vxe-column>
                    <vxe-column field="calc_thickness" title="计算厚度" :edit-render="{ autofocus: '.vxe-input--inner'}">
                        <template #edit="{ row }">
                            <vxe-input type="number" size="mini" v-model="row.calc_thickness" ></vxe-input>
                        </template>
                    </vxe-column>
                    <vxe-column title="操作" width="50">
                        <template #default="{ $rowIndex }">
                            <el-button size="small" text type="danger" icon="Delete"  @click="row.trans_data.splice($rowIndex,1)"></el-button>
                        </template>
                    </vxe-column>
                </vxe-table> -->
            </template>
        </vxe-column>
        <vxe-column title="操作" width="120">
            <template #default="{ row }">
                <div v-if="attrsTable.isEditByRow(row)">
                    <vxe-button size="small" status="success" icon="vxe-icon-save" @click="saveRow(row)"></vxe-button>
                    <vxe-button size="small" status="info" icon="vxe-icon-close" @click="attrsTable.clearEdit()"></vxe-button>
                </div>
                <div v-else>
                    <vxe-button size="small" status="primary" icon="vxe-icon-edit" @click="attrsTable.setEditRow(row)"></vxe-button>
                    <vxe-button size="small" status="danger" icon="vxe-icon-delete" @click="delRow(row)"></vxe-button>
                </div>
            </template>
        </vxe-column>
    </vxe-table>
</template>

<script>
import { GetCalcThicknessConfig,SaveData,DeleteData,GetAreas } from '@/api/bidCalcThicknessConfig'
import { ref } from 'vue'
import { VXETable, VxeTable } from 'vxe-table';
import {  allAreas  } from '@/calcRule/calc_config'
import  calcThicknessTable from '@/calcRule/CalcThicknessTable.vue'
export default {
    name: "bidCalcThicknessTable",
    components:{calcThicknessTable},
    setup() {
        const attrsTable = ref<VxeTable>({});
        return { attrsTable ,allAreas }
    },
    data() {
        return {
            tableLoading: true,
            tbData: [],
            
            currentArea:'北京',
            configAreas:null
        }
    },
    async mounted() {
        let query_area=this.$route.query.area;
        if(query_area)this.currentArea=query_area;
        await this.LoadData();
        this.configAreas=await GetAreas();
    },
    methods: {
        /**
         * 加载构件类型
         */
        async LoadData() {
            this.tableLoading = true;
            this.tbData = await GetCalcThicknessConfig(this.currentArea);
            this.tableLoading = false;
        },
        /**
         * enter键退出编辑
         * @param {object} e 
         */
         keydownEnter(e){
            if(e.code==='Enter') this.attrsTable.clearEdit();
        },
        async delRow(row){
            if (this.attrsTable.isInsertByRow(row)) {
                this.attrsTable.remove(row); 
                return;
            }
            let type=await VXETable.modal.confirm("您确定要删除该数据?","删除确认",{zIndex:9998});
            if(type==='confirm'){
                let result= await DeleteData(row.id);
                if(result){
                    VXETable.modal.message({content:'删除成功!',status:'success',zIndex:9999});
                }
                else{
                    VXETable.modal.message({content:'删除失败!',status:'error',zIndex:9999});
                }
                await this.LoadData();
            }
        },
        async newRow(){
            let temp_row=await this.attrsTable.insertAt({id:null,name:null,area:this.currentArea,trans_data:[]});
            await this.attrsTable.setEditRow(temp_row.row);
        },
        async saveRow(row){
            this.tableLoading = true;
            let postData={id:row.id,name:row.name,area:row.area};
            if(this.attrsTable.isInsertByRow(row))postData.id=null;
            postData.trans_data=JSON.stringify(row.trans_data.map(d=>{return {calc_thickness:d.calc_thickness,thickness:d.thickness}}));
            let result=  await SaveData([postData]);
            if(result){
                VXETable.modal.message({content:'保存成功!',status:'success',zIndex:9999});
            }
            else{
                VXETable.modal.message({content:'保存失败!',status:'error',zIndex:9999});
            }
            await this.LoadData();
        },
        showEdit(row){
            // row.temp_areas=row.area?.length>0?row.area?.split(','):null;this.attrsTable.setEditRow(row);
            if(this.attrsTable.isEditByRow(row)){
                this.attrsTable.clearEdit();
            }
            else{
                this.attrsTable.setEditRow(row);
            }
        },
        async areaChange(val){
            this.tbData=await GetCalcThicknessConfig(val);
            await this.attrsTable.reloadData(this.tbData);
        }
    }
}
</script>

<style scoped lang="scss">
:deep(.vxe-buttons--wrapper .el-select){
    margin-right: 12px;
}
.area-tag{
    margin-left: 4px;
    margin-bottom: 2px;
}
.area-series-select{
    width:84%;
    margin-right:5px;
}
:deep(.vxe-body--column .el-button){
    height:24px;
    padding: 5px 2px ;
}

.config-table{
    margin:0 10px 0 10px;
}
.config-add-btn{
    margin:0 10px 0 10px;
}
</style>