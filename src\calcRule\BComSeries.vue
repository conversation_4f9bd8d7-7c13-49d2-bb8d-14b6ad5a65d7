<template>
    <vxe-toolbar>
        <template #buttons>
            <vxe-button icon="vxe-icon-square-plus" @click="newRow">新增</vxe-button>
            <vxe-button icon="vxe-icon-save" @click="saveTable" >保存</vxe-button>
        </template>
    </vxe-toolbar>
    <vxe-table ref="attrsTable" height="500" show-overflow keep-source 
        :edit-config="{ trigger: 'dblclick', showStatus: true, mode: 'cell' }" 
        :row-config="{ useKey: true, isHover: true,  keyField: 'id' }" :loading="tableLoading" :data="tbData">
        <vxe-column title="#" width="60" type="seq" />
        <vxe-column title="地区" width="100" field="area" align="center" :edit-render="{  autofocus: '.vxe-input--inner' }" >
            <template #edit="{ row }">
                <el-select v-model="row.area" persistent :teleported="false" placeholder="地区"  >
                    <el-option v-for="area in allAreas" :key="area" :value="area" :label="area" ></el-option>
                </el-select>
            </template>
        </vxe-column>
        <vxe-column title="定额系列"  field="series" align="center" :edit-render="{  autofocus: '.vxe-input--inner' }" >
            <template #edit="{ row }">
                <vxe-input v-model="row.series" :onkeypress="keydownEnter" ></vxe-input>
            </template>
        </vxe-column>
        <vxe-column title="备注" width="200" field="remarks" align="center" :edit-render="{  autofocus: '.vxe-input--inner' }" >
            <template #edit="{ row }">
                <vxe-input v-model="row.remarks" :onkeypress="keydownEnter" ></vxe-input>
            </template>
        </vxe-column>
        <vxe-column title="类型" width="100" field="type" align="center" :edit-render="{  autofocus: '.vxe-input--inner' }" >
            <template #default="{ row }">
                <el-text v-if="row.type">{{ row.type==1?'定额':'清单' }}</el-text>
            </template>
            <template #edit="{ row }">
                <el-select v-model="row.type" persistent :teleported="false" placeholder="类型">
                    <el-option value="1" label="定额"></el-option>
                    <el-option value="2" label="清单"></el-option>
                </el-select>
            </template>
        </vxe-column>
        <!-- <vxe-column title="定额系列"  field="series" :edit-render="{ }">
            <template #default="{row}">
                <el-tag class="area-tag" v-if="row.series?.length>0" v-for="(val,index) in row.series?.split(',')" disable-transitions >{{ val }}</el-tag>
            </template>
            <template #edit="{row}">
                <vxe-input v-model="row.series" v-show="false" ></vxe-input>
                <el-tag class="area-tag" v-if="row.series?.length>0" v-for="(val,index) in row.series?.split(',')" disable-transitions :key="val" closable @close="valDel(index,row)"  >{{ val }}</el-tag>
                <el-input v-if="row.inputVisible" v-model="row.inputValue"  size="small" @keyup.enter="inputEnter(row)"  @blur="inputEnter(row)" style="width: 120px;" />
                <el-button v-else size="small" @click="row.inputVisible=true" style="margin-left: 5px;" >+ 添加新值</el-button>
            </template>
        </vxe-column> -->
        <vxe-column title="操作" width="70">
            <template #default="{row}">
                <vxe-button icon="vxe-icon-delete" @click="delRow(row)" ></vxe-button>
            </template>
        </vxe-column>
    </vxe-table>
</template>

<script>
import { GetBidCalcSeries,SaveData,DeleteData } from '@/api/bidCalcSeries'
import {ref} from 'vue'
import { VXETable, VxeTable } from 'vxe-table';
import { allAreas } from '@/calcRule/calc_config'
export default {
    name: "bComSeries",
    setup() {
        const attrsTable = ref<VxeTable>({});
        return { attrsTable,allAreas  }
    },
    data() {
        return {
            tableLoading: true,
            atLoading: false,
            tbData: [],
            localAttributes: [],
        }
    },
    async mounted() {
        await this.LoadData();
    },
    methods: {
        /**
         * 加载定额系列
         */
        async LoadData() {
            this.tableLoading = true;
            this.tbData = await GetBidCalcSeries();
            this.tableLoading = false;
        },
        /**
         * enter键退出编辑
         * @param {object} e 
         */
         keydownEnter(e){
            if(e.code==='Enter') this.attrsTable.clearEdit();
        },
        async delRow(row){
            if (this.attrsTable.isInsertByRow(row)) {
                this.attrsTable.remove(row); 
                return;
            }
            let type=await VXETable.modal.confirm("您确定要删除该数据?","删除确认",{zIndex:9998});
            if(type==='confirm'){
                let result= await DeleteData(row.id);
                if(result){
                    VXETable.modal.message({content:'删除成功!',status:'success',zIndex:9999});
                }
                else{
                    VXETable.modal.message({content:'删除失败!',status:'error',zIndex:9999});
                }
                this.LoadData();
            }
        },
        async newRow(){
            let temp_row=await this.attrsTable.insertAt({id:null,area:null,series:null});
            await this.attrsTable.setEditRow(temp_row.row);
        },
        async saveTable(){
            let tempArr= this.attrsTable.getUpdateRecords()??[];
            let inserts=this.attrsTable.getInsertRecords()??[];
            tempArr.push(...inserts.map(t=>{return {id:null,area:t.area,series:t.series,remarks:t.remarks}}));
            if(tempArr?.length>0){
               let result=  await SaveData(tempArr);
               if(result){
                VXETable.modal.message({content:'保存成功!',status:'success',zIndex:9999});
               }
               else{
                VXETable.modal.message({content:'保存失败!',status:'error',zIndex:9999});
               }
               this.LoadData();
            }
        },
        // valDel(index,row){
        //     let tempArr=row.series.split(',');
        //     tempArr.splice(index,1);
        //     row.series=tempArr.toString();
        // },
        inputEnter(row)
        {
            if(row.inputValue){
                let tempArr=row.series?.split(',')??[];
                tempArr.push(row.inputValue);
                row.series=tempArr.toString();
            }
            delete row.inputVisible;
            delete row.inputValue
        }
        
    }
}
</script>

<style scoped lang="scss">
.dropdown {
    border-radius: 4px;
    border: 1px solid #dcdfe6;
    background-color: #fff;
}

.list-item {
    line-height: 22px;
}

.list-item:hover {
    background-color: #f5f7fa;
}

</style>