<template>
    <el-tag type="success" effect="light" disable-transitions>
        <el-text size="small" >{{ attrLabel }}</el-text>
        <el-text size="small" type="success" v-if="attrType=='enum'" v-for="(cval,vindex) in conds[attrName]" > {{ cval }} <el-text size="small" type="warning" v-if="vindex<conds[attrName].length-1"> 或 </el-text></el-text>
        <el-text size="small" type="success" v-else-if="attrType=='range'" >{{ conds[attrName][0] }} ~ {{ conds[attrName][1] }}</el-text>
        <el-text size="small" type="success" v-else-if="attrType=='u-switch'" >{{ conds[attrName]?'&#10003;':'&#10005;' }}</el-text>
    </el-tag>
</template>
<script>

import {wkCondOptions}  from '@/calcRule/calc_config'
export default {
    name: 'attrText',
    props: {
        /**
         * 属性名
         */
        attrName:String,
        /**
         * 属性
         */
        conds: Object,
    },
    mounted(){
        // this.attrType= this.attrTypes.get(this.attrName)
        this.attrType= wkCondOptions.find(p=>p.value==this.attrName)?.type;
    },
    setup(){
        return {wkCondOptions}
    },
    data(){
        return {
            /**
             * 属性名-类型
             */
            // attrTypes:new Map([
            //     ['kd','range'],
            //     ['mc','enum'],
            //     ['cz','enum'],
            //     ['fcz','enum'],
            //     ['kdgzz','u-switch'],
            //     ['zdtb','u-switch'],
            //     ['dsjsj','u-switch'],
            //     ['jclx','u-switch'],
            //     ['bpzh','u-switch']
            // ]),
            /**
             * * 属性类型
             * */
            attrType: null,
            attrLabel:wkCondOptions.find(w=>w.value==this.attrName)?.label??this.attrName
        }
    },
    methods:{
    },
}
</script>

<style scoped>
.el-text+.el-text{
    margin-left: 4px;
}
.el-tag+.el-tag{
    margin-left: 4px;
}
</style>
