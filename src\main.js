import Vue from 'vue'
import { createApp } from 'vue'
import App from './App.vue'
import router from './router'
import element from 'element-plus'
import 'element-plus/dist/index.css'
import axios from "axios";
import VueAxios from "vue-axios";
import * as ElementPlusIconsVue from '@element-plus/icons-vue'
import VXETable from 'vxe-table'
import 'vxe-table/lib/style.css'

var app=createApp(App);
app
.use(router)
.use(element)
.use(VueAxios,axios)
.use(VXETable);

Object.keys(ElementPlusIconsVue).forEach((key)=>{
    app.component(key,ElementPlusIconsVue[key]);
});

axios.defaults.baseURL=process.env.VUE_APP_BASE_API;
// axios.defaults.timeout=2000;
app.config.globalProperties.$axios=axios;

router.beforeEach((to,from,next)=>{
    if(to.meta.title){
        document.title=to.meta.title;
    }
    next();
});
// app.config.unwrapInjectedRef = true;
// app.use(ElementPlusIconsVue);
app.mount('#app');
