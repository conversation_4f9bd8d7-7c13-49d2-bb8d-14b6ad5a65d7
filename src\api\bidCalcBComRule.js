import axios from "axios"

/**
 * 根据构件分类查询
 * @returns 
 */
export function QueryByClassifications(series,classifications){
    return axios.post(`/BidCalcBComRule/QueryByClassifications`,{id:series,val:classifications})
        .then((res) => {
            return res.data.data;
        })
}

/**
 * 保存数据
 * @param {Object} data 
 * @returns 
 */
export function SaveAsync(data){
    return axios.post(`/BidCalcBComRule/Save`, data)
    .then((res) => {
        return  res?.data?.succeeded;
    })
}

/**
 * 删除数据
 * @param {Object} data 
 * @returns 
 */
 export function DeleteAsync(data){
    return axios.post(`/BidCalcBComRule/Delete`, data)
    .then((res) => {
        return  res?.data?.succeeded;
    })
}

/**
 * 查重
 * @param {Object} data 
 * @returns 
 */
 export function ExistsAsync(data){
    return axios.post(`/BidCalcBComRule/Exists`, data)
    .then((res) => {
        return  res?.data?.data;
    })
}

/**
 * 获取所有地区的构件数量
 * @returns 
 */
 export function QueryAreaCount(){
    return axios.get(`/BidCalcBComRule/QueryAreaCount`)
    .then((res) => {
        return  res?.data?.data;
    })
}


/**
 * 按地区复制构件数据
 * @param {Object} data 
 * @returns 
 */
 export function TransferData(data){
    return axios.post(`/BidCalcBComRule/TransferData`, data)
    .then((res) => {
        return  res?.data?.data;
    })
}


/**
 * 获取构件分类
 * @returns 
 */
 export function QueryClassifications(){
    return axios.get(`/BidCalcBComRule/QueryClassifications`)
    .then((res) => {
        return  res?.data?.data;
    })
}




/**
 * 复制构件数据
 * @returns 
 */
 export function CopyData(bcom,newBCom,newClassification){
    return axios.post(`/BidCalcBComRule/CopyData`,{bcom:bcom,newBCom:newBCom,newClassification:newClassification})
    .then((res) => {
        return  res?.data?.succeeded;
    })
}


/**
 * 获取关联地区
 * @returns 
 */
 export function GetRelationArea(area,series){
    return axios.get(`/BidCalcBComRule/GetRelationArea?area=${area}&series=${series}`)
    .then((res) => {
        return  res?.data?.data;
    })
}

/**
 * 保存单个地区关联构件数据
 * @returns 
 */
 export function SaveRelationArea(area,series,ids,cids){
    return axios.post(`/BidCalcBComRule/SaveRelationArea`,{area:area,series:series,ids:ids,classifications:cids})
    .then((res) => {
        return  res?.data?.succeeded;
    })
}

/**
 * 保存单个构件关联地区
 * @returns 
 */
 export function SaveRelationAreas(id,vals){
    return axios.post(`/BidCalcBComRule/SaveRelationAreas`,{id:id,val:vals})
    .then((res) => {
        return  res?.data?.succeeded;
    })
}


/**
 * 从同一地区删除多个规则
 * @returns 
 */
 export function DeleteRelationAreasBySeries(series,vals){
    return axios.post(`/BidCalcBComRule/DeleteRelationAreasBySeries`,{id:series,val:vals})
    .then((res) => {
        return  res?.data?.succeeded;
    })
}


/**
 * 关联多个规则到同一地区
 * @returns 
 */
 export function SaveRelationAreasBySeries(series,vals){
    return axios.post(`/BidCalcBComRule/SaveRelationAreasBySeries`,{id:series,val:vals})
    .then((res) => {
        return  res?.data?.succeeded;
    })
}
