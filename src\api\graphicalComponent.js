import axios from "axios"
//process.env.VUE_APP_ZSK_API
const apiInstance=axios.create({});

/**
 * 获取所有图形构件
 * @returns 
 */
export function GetGraphicalComponents(){
    
    return apiInstance.post(`http://192.168.50.4:801/baseword/NewComponentSystem/query`,{specialty_id:0,parent_key:"",type:0,tab_type:0,active_name:"graphicsent"})
        .then((res) => {
            return res.data.data;
        })
}


