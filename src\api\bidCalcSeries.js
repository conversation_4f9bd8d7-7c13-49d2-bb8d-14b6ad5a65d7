import axios from "axios"

/**
 * 获取定额系列
 * @returns 
 */
export function GetBidCalcSeries(){
    return axios.get(`/BidCalcBCom/GetBidCalcSeries`)
        .then((res) => {
            return res.data.data;
        })
}

/**
 * 保存定额系列
 * @param {Object} data 
 * @returns 
 */
export function SaveData(data){
    return axios.post(`/BidCalcBCom/SaveBidCalcSeries`, data)
    .then((res) => {
        return  res?.data?.succeeded;
    })
}

/**
 * 删除定额系列
 * @param {Number} id
 * @returns 
 */
export function DeleteData(id){
    return axios.post(`/BidCalcBCom/DelBidCalcSeries`, {id:id})
    .then((res) => {
        return res?.data?.succeeded;
    })
}

