import axios from "axios"

/**
 * 获取砌体墙厚度配置
 * @returns 
 */
export function GetCalcThicknessConfig(area){
    return axios.get(`/BidCalcConfig/GetCalcThicknessConfig?area=${area??''}`)
        .then((res) => {
            let result=res.data.data;
            if(result){
                result.forEach(d => {
                    d.trans_data=JSON.parse(d.trans_data);
                });
            }
            return result;
})
}

/**
 * 保存
 * @param {Object} data 
 * @returns 
 */
export function SaveData(data){
    return axios.post(`/BidCalcConfig/SaveCalcThicknessConfig`, data)
    .then((res) => {
        return  res?.data?.succeeded;
    })
}

/**
 * 删除
 * @param {Number} id
 * @returns 
 */
export function DeleteData(id){
    return axios.post(`/BidCalcConfig/DelCalcThicknessConfig`, {id:id})
    .then((res) => {
        return res?.data?.succeeded;
    })
}


/**
 * 获取已配置地区
 * @returns 
 */
 export function GetAreas(){
    return axios.get(`/BidCalcConfig/GetCalcThicknessConfigAreas`)
        .then((res) => {
            return res.data.data;
        })
}
