import { v4 as uuidv4 } from 'uuid'

/**
* 条件类型
*/
 export const condOptions=[
    // { value: "1-1", label: "属性判断" },
    // { value: "1-4", label: "属性判断(带系数)" },
    { value: "1-2", label: "属性值比较" },
    { value: "1-3", label: "属性值范围" },
    { value: "2-1", label: "位置判断" },
    // { value: "3-1", label: "扣减部位" } //只在 “扣减条件”中单独设置
    // { value: "4-1", label: "运算图形" } //在 非“扣减条件”中单独设置
 ]
/**
 * 比较选项
 */
 export const compateOptions= [
    // { value: "1-1", label: "是",showLabel:":" },
    // { value: "1-2", label: "不是",showLabel:"!:" },
    { value: "1-3", label: "是", showLabel: "是" },
    { value: "1-4", label: "不是", showLabel: "非" },
    { value: "1-5", label: "大于", showLabel: ">" },
    { value: "1-6", label: "大于等于", showLabel: "≥" },
    { value: "1-7", label: "小于", showLabel: "<" },
    { value: "1-8", label: "小于等于", showLabel: "≤" },
]
/**
* 位置选项
*/
export const positionOptions= [
    { value: "2-1", label: "上面", showLabel: "上面" },
    { value: "2-2", label: "下面", showLabel: "下面" },
    { value: "2-3", label: "左侧", showLabel: "左侧" },
    { value: "2-4", label: "右侧", showLabel: "右侧" },
    { value: "2-5", label: "内部", showLabel: "内部" },
    { value: "2-6", label: "包含", showLabel: "包含" },
    { value: "2-7", label: "平行相交", showLabel: "平行相交" },
    { value: "2-8", label: "非平行相交", showLabel: "非平行相交" },
    { value: "2-10", label: "相交", showLabel: "相交" },
    { value: "2-9", label: "不相交", showLabel: "不相交" },
    { value: "2-11", label: "底部平齐", showLabel: "底部平齐" },
    { value: "2-12", label: "同厚", showLabel: "同厚" },
    { value: "2-13", label: "外侧", showLabel: "外侧" },
    { value: "2-14", label: "相切", showLabel: "相切" },
]

/**
 * 扣减部位
 */
export const deductParts=[
    { value: "3-1", label: "凸出墙面部分", showLabel: "凸出墙面部分" },
    { value: "3-2", label: "地沟空洞部分", showLabel: "地沟空洞部分" },
]

/**
 * 土方扣减条件
 */
export const tfDeductLimit=[
    { value: "4-1", label: "算至基础边", showLabel: "算至基础边" },
    { value: "4-2", label: "算至垫层边", showLabel: "算至垫层边" },
    { value: "4-3", label: "算至工作面边", showLabel: "算至工作面边" },
    { value: "4-4", label: "算至垫层/基础边", showLabel: "算至垫层/基础边" },
    { value: "4-5", label: "仅放坡重叠", showLabel: "仅放坡重叠" },
]

/**
 * 扣减项
 */
 export const deductOptions= [
    { value: 1, label: "扣除" },
    { value: 2, label: "不扣除" },
    // { value: 3, label: "扣除平行相交部分,不扣除非平行相交部分" },
    // { value: 4, label: "扣除非平行相交部分,不扣除平行相交部分" },
    // { value: 5, label: "扣除凸出墙面" },
    // { value: 6, label: "不扣除凸出墙面" }

]
/**
* 地区
*/
export const allAreas= ['北京', '四川', '湖南', '天津', '青海', '云南', '新疆', '河北', '吉林', '黑龙江',
    '甘肃', '宁夏', '广东', '浙江', '重庆', '山西', '安徽', '湖北', '辽宁', '内蒙古','福建',
    '海南', '江西', '上海', '深圳', '山东', '河南', '贵州', '西藏', '江苏', '广西','陕西']


/**
* 计算规则类型
*/
export const ruleTypes= [
    { value: 5, label: "计算公式" },
    { value: 1, label: "计算系数" },
    { value: 2, label: "属性转换" },
    { value: 3, label: "延伸计算" },
    { value: 4, label: "砌体墙厚度计算表" },
    { value: 6, label: "面积融合(平整场地)" },
    { value: 7, label: "保温层计算长度" },
    { value: 8, label: "基础扣减宽度" },
    { value: 9, label: "风管堵头计算" },
]
export const jzmjTypes=[
    {value:1,label:'首层建筑面积'},
    {value:2,label:'最大地下室建筑面积'},
    {value:3,label:'首层与地下建筑面积合并'}
]

export const bwccdTypes=[
    {value:1,label:'墙边线'},
    {value:2,label:'中心线'}
]


/**
 * 属性取值方式
 */
export const attrValuedTypes= [
    { value: 0, label: "内置属性" },
    { value: 1, label: "数值" },
    { value: 2, label: "构件属性" },
    // { value: 3, label: "构件属性(含系数)" },
    // { value: 4, label: "水平投影面积" },
    // { value: 5, label: "垂直投影面积" },
    // { value: 6, label: "质量" }
]

export const  newUUID=() => uuidv4().replaceAll('-', '')

/**
* 是否多个值
*/
export const  isMultiple=(op) => (op === '1-3' || op === '1-4')


/**
* 添加条件
* @param {Object} row 条件所属对象
* @param {Object} condsAttr 条件字段名称
* @param {String} op 条件类型 
*/
export function addCond(row,condsAttr,op) {
    if(!row[condsAttr])row[condsAttr]=[];
    let tempCond = null;
    switch (op) {
        //属性值比较
        case '1-1':
            tempCond = { ctype: op, attr: null, op: null, tbcom: null, tattr: null };
            break;
        //属性值比较(带系数)
        case '1-4':
            tempCond = { ctype: op, attr: null, op: null, tbcom: null, tattr: null, factor: 1 };
            break;
        //
        case '1-2':
            tempCond = { ctype: op, attr: null, op: null, vals: [] };
            break;
        //范围
        case '1-3':
            tempCond = { ctype: op, attr: null, op: null, sval: null, eval: null };
            break;
        //位置
        case '2-1':
            tempCond = { ctype: op, bcom: null, position: null };
            break;
        //扣减部位
        case '3-1':
            tempCond = { ctype: op, deductpart: null };
            break;
          //运算图形
        case '4-1':
            tempCond = { ctype: op, calcgraphic: null };
            break;
        default:
            break;
    }
    if (tempCond) row[condsAttr].push(tempCond);
}

/**
 * 删除条件
 */
 export function delCond(conds,cIndex) {
    if(conds && cIndex!==undefined){
        let delTemp=conds[cIndex];
        if(delTemp?.or?.length>0){
            let orTemp=delTemp.or[0];
            if(orTemp.type==1){
                delTemp.type=orTemp.type;
                delTemp.attr=orTemp.attr;
                delTemp.op=orTemp.op;
                delTemp.val=orTemp.val;
            }
            else if(orTemp.type==2){
                delTemp.type=orTemp.type;
                delTemp.bcom=orTemp.bcom;
                delTemp.position=orTemp.position;
            }
            delTemp.or.splice(0,1);
        }
        else{
            conds.splice(cIndex,1);
        }
    }
}

/**
 * 工作面宽度条件
 */
export const wkCondOptions=[
    { value: "cz", label: "材质" ,type:'enum'},
    { value: "fcz", label: "非-材质" ,type:'enum'},
    { value: "mc", label: "垂直面层",type:'enum' },
    { value: "kdgzz", label: "坑底灌注桩",type:'u-switch' },
    { value: "kd", label: "管道结构宽" ,type:'range'},
    { value: "zdtb", label: "支挡土板" ,type:'u-switch'},
    { value: "dsjsj", label: "搭设脚手架" ,type:'u-switch'},
    { value: "jclx", label: "基础类型" ,type:'enum'},
    { value: "bpzh", label: "边坡支护",type:'u-switch' },
    { value: "dxsmc3mys", label: "地下室埋深超3m以上",type:'u-switch' },
    { value: "dtbms", label: "挡土板面数" ,type:'enum'},
]

/**
 * 基础/垫层/管沟 材质
 */
export const wkCondCZs=["砖","毛石","条石","方整石","块石","混凝土","钢筋混凝土","水泥管","混凝土管","钢筋混凝土管"]

 /**
 * 基础立面做面层
 */
export const wkCondMCs=["防水层","防腐层","防潮层","砂浆防潮层"]

 /**
 * 基础类型
 */
  export const wkCondJCLXs=["条形基础","独立基础"]

/**
 * 挡土板面数
 */
    export const wkCondDTBMS=["单面","双面"]

/**
 * 二次生成配置keys
 */
export const sgConfigKeys=[
    {value:'workspace_width',label:'工作面宽度(mm)'},
    {value:'doorway_extend',label:'楼地面水平防水在门窗洞口水平开口处的延伸长度(mm)'},
    {value:'stairs_extend',label:'楼梯连接楼地面最后一阶延伸长度(mm)'},
    {value:'metope_plaster_extend',label:'内墙面装修抹灰遇吊顶时延伸高度(mm)'},
    {value:'metope_material_extend',label:'内墙面装修块料遇吊顶时延伸高度(mm)'},
    {value:'independent_column_plaster_extend',label:'室内独立柱装修抹灰遇吊顶时延伸高度(mm)'},
    {value:'independent_column_material_extend',label:'室内独立柱装修块料遇吊顶时延伸高度(mm)'},
    {value:'step_extend',label:'台阶最上层踏步延伸长度(mm)'},
    {value:'step_surface_extend',label:'台阶面层最上层踏步延伸长度(mm)'},
]

/**
 * 图形构件属性
 */
 export const calcAttrs=[
    { value: "CZ", label: "材质" ,values:["商品混凝土"]},
    { value: "GJZL", label: "柱种类",values:["框架柱","暗柱"] },
    { value: "HD", label: "厚度" },
    { value: "LB", label: "类别",values:["次梁","有梁板"] },
    { value: "JMGD", label: "截面高度" },
    { value: "JMKD", label: "截面宽度" },
    { value: "Type", label: "梁类型",values:["梁","基础梁"] },
    { value: "JGLB", label: "梁种型",values:["屋面框架梁","非框架梁","楼层框架梁"] },
    { value: "SFSLB", label: "是否是楼板" },
]

/**
 * 土质类别
 */
export const soilCategories=[
    {value:0,label:'无'},
    {value:1,label:'一类土'},
    {value:2,label:'二类土'},
    {value:3,label:'三类土'},
    {value:4,label:'四类土'},
    {value:5,label:'一般土'},
    {value:6,label:'砂砾坚土'},
    {value:7,label:'采用降水措施'}
]