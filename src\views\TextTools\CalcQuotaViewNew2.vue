 <!--
      构件指标计算视图组件
      功能：
      1. 显示指标计算结果
      2. 提供指标异常检测功能
    -->
    <template>
        <div v-loading.fullscreen.lock="viewLoading" element-loading-text="加载中..."  style="width: 100%;height: 100%;" >
        <el-space wrap v-loading="viewLoading" element-loading-text="加载中..."  style="width: 100%;height: 100%;" >
            <el-card shadow="hover" class="calc-card" v-for="adata in showData" :key="adata.quota_id + '_' + adata.result_index">
                <div class="calc-tab" :ref="el => setChartRef(el, adata?.quota_id,adata?.result_index,adata?.quota_type)" ></div>
            </el-card>
        </el-space>
        <el-empty v-if="!viewLoading && (!showData || showData?.length==0)" description="未检测到指标异常!" />
    </div>
    </template>
    
    <script>
    import { ref } from 'vue'
    import { VxeTable } from 'vxe-table'
    import Highcharts from 'highcharts'
    import HighchartsMore from 'highcharts/highcharts-more'
    // import exporting from 'highcharts/modules/exporting'
    import { HoujComponentQuota, GetAllQuota } from '@/api/quota_userProjectResult'
    import { ElMessage } from 'element-plus';
    import { groupBy } from 'lodash-es'

    Highcharts.setOptions({ lang: { locale: 'zh-CN' } });
    
    export default {
        data() {
            return {
                viewLoading:true,
                allQuota: [],
                allData: [],
                showData: [],
                hasHard:true
            }
        },
        async mounted() {
            this.viewLoading=true;
            this.allQuota = await GetAllQuota();
            this.allQuota.forEach(q=>{
                q.jsonData=JSON.parse(q.jsonData);
                q.component_name=q.jsonData.bcomsa[0].bcom;
                q.specialty=q.jsonData.bcomsa[0].bcomIds[0];
            });
            this.viewLoading=false;
            window.addEventListener('message', async (e) => {
                if (e.data.type == 'init') {
                    let userprojectid = e.data.userprojectid;
                    let buildingid = e.data.buildingid;
                    let speciltyids = e.data.speciltyids;
                    let xgj_entnames = e.data.xgj_entnames;
                    let entobjdataids = e.data.entobjdataids;
                    let floornames = e.data.floornames;
                    // 验证必要参数
                    if (!userprojectid || !buildingid) {
                        ElMessage.error('缺少必要参数')
                    }
                    await this.houjquota(userprojectid, buildingid, speciltyids, xgj_entnames, floornames, entobjdataids);
                }
            });
            window.parent.postMessage({type:'mounted'},'*');
        },
        methods: {
            //  chartsInit(chart) {
            //     // 动态生成自定义图例
            //     const plotBands = chart.yAxis[0].plotLinesAndBands;
            //     let temp_x = 50; // 图例起始 X 坐标
            //     const legendY = 480; // 图例起始 Y 坐标
            //     plotBands.filter(p=>p?.options?.from).forEach((band, index) => {
            //         temp_x += index*140;
            //         // 创建图例项的颜色块
            //         chart.renderer.rect(temp_x, legendY,18,13)
            //             .attr({
            //                 fill: band.options.color,
            //                 // stroke: 'black',
            //                 // 'stroke-width': 0.5
            //             })
            //             .add();
            //             // .on('click', function () {
            //             //     // 点击图例时切换 plotBand 的显示
            //             //     band.svgElem.attr({ visibility: band.visible ? 'hidden' : 'visible' });
            //             //     band.visible = !band.visible;
            //             // });

            //         // 创建图例项的文本标签
            //         chart.renderer.text(band.options.description, temp_x+23, legendY+10)
            //             .attr({ align: 'left' })
            //             .css({ fontSize: '12px' })
            //             .add();
            //     });
            //     if(chart.quota_multiples){
            //         chart.renderer.rect(temp_x, legendY,18,13).attr({fill: 'red'}).add();
            //         chart.renderer.text(chartOptions.quota_multiples, temp_x+23, legendY+10).attr({ align: 'left' }).css({ fontSize: '12px' }).add();
            //     }
            //     if(chart.quota_fixedValues){
            //         chart.renderer.rect(temp_x, legendY,18,13).attr({fill: 'red'}).add();
            //         chart.renderer.text(chartOptions.quota_fixedValues, temp_x+23, legendY+10).attr({ align: 'left' }).css({ fontSize: '12px' }).add();
            //     }
            // },
             getChartConfig(quota_id,result_index,qtype){
                let temp_result = null;
                let temp_quota = this.allQuota.find(d => d.id == quota_id);
                if (temp_quota) {
                    let results = temp_quota?.jsonData?.results;
                    if (results?.length > 0) temp_result = results[result_index];
                }
                var chartOptions={
                    chart:{ height:'492px'},
                    accessibility: { enabled: false },
                    credits: { enabled: false },
                    exporting: { enabled: false },
                    legend: { enabled: false},
                    title: { text: null },
                    tooltip:{backgroundColor:'#e2e2e2',color:'black'},
                    subtitle: { text: null }
                };
                if (temp_result) {
                    let colorObj= { 标准: '#ffdc60', 最佳: '#9fe080', 异常: '#ffdc60 ' };
                    let getDesc = (val)=>{
                        if (val > temp_result.result_hard_max || val<temp_result.result_hard_min) return'异常';
                        else if (val > temp_result.result_soft_min && val < temp_result.result_soft_max) return `最佳(${temp_result.result_soft_min}~${temp_result.result_soft_max})`;
                        else if (val > temp_result.result_hard_min && val < temp_result.result_hard_max) return `标准(${temp_result.result_hard_min}~${temp_result.result_hard_max})`;
                        else return null;
                    };
                    // let temp_arr = this.allData.flatMap(a => a.quotavalues2).filter(a => a.quota_id == quota_id).flatMap(a => a.all_data);
                    //只使用第一个
                    let temp_arr = this.showData.filter(a => a.quota_id == quota_id).flatMap(a => a.all_data);
                    //标题
                    chartOptions.title.text =  temp_quota.title;
                    //自定义副标题
                    // chartOptions.subtitle.useHTML=true;
                    // chartOptions.subtitle.text='';
                    // if(temp_result?.result_soft_min||temp_result?.result_soft_max) chartOptions.subtitle.text+=`<span class="legend-box" style="background-color: #9fe080;"></span><span>最佳: ${temp_result?.result_soft_min}~${temp_result?.result_soft_max}</span>`;
                    // if(temp_result?.result_hard_min||temp_result?.result_hard_max) chartOptions.subtitle.text+=`<span class="legend-box" style="background-color: #ffdc60;"></span><span>标准: ${temp_result?.result_hard_min}~${temp_result?.result_hard_max}</span>`;
                    // // if(temp_result?.multiples>0) chartOptions.subtitle.text+=`<span class="legend-box" style="background-color: #9fe080;"></span><span>倍数: ${temp_result?.multiples}</span>`;
                    // // if(temp_result?.fixedValues?.length>0) chartOptions.subtitle.text+=`<span class="legend-box" style="background-color: #9fe080;"></span><span>固定值: ${temp_result?.fixedValues?.join(',')}</span>`;
                    // chartOptions.subtitle.text +=`<span class="legend-box" style="background-color: red;"></span><span>异常</span>`;
                    if(qtype==1){
                        /**
                         * Gauge-Solid 图表配置：参考highcharts gauge-solid类型
                         * @description 使用实心仪表盘显示指标分布，按temp_result指标值划分刻度
                         * @feature 仪表盘样式、自定义刻度、数据统计显示
                         * @performance O(n) - n为数据点数量
                         */

                        // REQUIRE: 更换为gauge-solid图表类型
                        chartOptions.chart = {
                            type: 'solidgauge',
                            height: '400px',
                            backgroundColor: 'transparent'
                        };

                        // FEATURE: 计算数据统计用于仪表盘显示
                        const allValues = temp_arr.map(d => Number(d.val));
                        const totalCount = allValues.length;
                        const avgValue = allValues.reduce((sum, val) => sum + val, 0) / totalCount;
                        const maxValue = Math.max(...allValues);

                        // EXTENSION POINT: 按temp_result指标值划分仪表盘刻度
                        const gaugeTickPositions = [
                            0,
                            temp_result.result_hard_min || 0,
                            temp_result.result_soft_min || 0,
                            temp_result.result_soft_max || 0,
                            temp_result.result_hard_max || 0,
                            Math.max(maxValue, (temp_result.result_hard_max || 0) * 1.2)
                        ].filter((value, index, array) => array.indexOf(value) === index && value >= 0)
                         .sort((a, b) => a - b);



                        chartOptions.title = {
                            text: `平均值: ${avgValue.toFixed(1)}`,
                            style: { fontSize: '16px', fontWeight: 'bold' }
                        };

                        chartOptions.pane = {
                            center: ['50%', '85%'],
                            size: '140%',
                            startAngle: -90,
                            endAngle: 90,
                            background: {
                                backgroundColor: '#EEE',
                                innerRadius: '60%',
                                outerRadius: '100%',
                                shape: 'arc'
                            }
                        };

                        chartOptions.tooltip = {
                            enabled: false
                        };

                        // FEATURE: 按指标值划分Y轴刻度，类似gauge-solid
                        chartOptions.yAxis = {
                            min: 0,
                            max: gaugeTickPositions[gaugeTickPositions.length - 1],
                            tickPositions: gaugeTickPositions,
                            lineWidth: 0,
                            tickWidth: 0,
                            minorTickInterval: null,
                            tickAmount: 2,
                            title: { y: -70 },
                            labels: {
                                useHTML: true,
                                y: 16,
                                formatter: function() {
                                    const currentValue = this.value;
                                    let labelText = currentValue.toFixed(0);
                                    let labelColor = '#666';

                                    // REQUIRE: 根据指标类型设置标签样式
                                    if (currentValue === temp_result.result_soft_min) {
                                        labelText = '软最小';
                                        labelColor = '#4ecdc4';
                                    } else if (currentValue === temp_result.result_soft_max) {
                                        labelText = '软最大';
                                        labelColor = '#4ecdc4';
                                    } else if (currentValue === temp_result.result_hard_min) {
                                        labelText = '硬最小';
                                        labelColor = '#ffa726';
                                    } else if (currentValue === temp_result.result_hard_max) {
                                        labelText = '硬最大';
                                        labelColor = '#ffa726';
                                    }

                                    return `<span style="color: ${labelColor}; font-size: 10px; font-weight: bold;">${labelText}<br/>${currentValue.toFixed(0)}</span>`;
                                }
                            }
                        };

                        // OPTIMIZATION: 创建多个仪表盘系列显示不同区间的数据
                        chartOptions.series = [];

                        // 按描述分组数据
                        let descgroup = groupBy(temp_arr.map(d => {
                            let number_val = Number(d.val);
                            return { val: number_val, des: getDesc(number_val) }
                        }), 'des');

                        let seriesIndex = 0;
                        for(let k of Object.keys(descgroup)){
                            const groupData = descgroup[k];
                            const groupAvg = groupData.reduce((sum, item) => sum + item.val, 0) / groupData.length;
                            const groupCount = groupData.length;

                            chartOptions.series.push({
                                name: k,
                                data: [{
                                    color: colorObj[k.substring(0,2)] || '#666',
                                    radius: `${100 - seriesIndex * 15}%`,
                                    innerRadius: `${85 - seriesIndex * 15}%`,
                                    y: groupAvg
                                }],
                                dataLabels: {
                                    useHTML: true,
                                    enabled: true,
                                    format: `<div style="text-align: center; font-size: 12px;">
                                                <span style="font-size: 14px; font-weight: bold;">{point.y:.1f}</span><br/>
                                                <span style="color: #666; font-size: 10px;">${k} (${groupCount}个)</span>
                                             </div>`,
                                    borderWidth: 0,
                                    y: -20 - seriesIndex * 30
                                }
                            });
                            seriesIndex++;
                        }

                        // 移除不需要的配置
                        delete chartOptions.xAxis;
                        delete chartOptions.legend;
                        delete chartOptions.plotOptions;
                    }
                    else if (qtype == 2 && this.hasHard) {
                        // 计算参考值用于倍数划分
                        const refValue = Math.max(
                            temp_result.result_soft_max || 0,
                            temp_result.result_hard_max || 0,
                            1
                        );
                        let temp_max= refValue * 3;
                        // 散点
                        chartOptions.tooltip.formatter = function () {
                            let temp_title = '指标值';//this.series.name;
                            if (this.fixedValues) {
                                temp_title = '非固定值';
                            }
                            else if (this.multiples) {
                                temp_title = '非倍数';
                            }
                            else if (this.noKeyValue) {
                                temp_title = '无数据';
                            }
                            return `楼层: ${this.floorname}  ${temp_title}: ${this.value}`;
                        };

                        chartOptions.xAxis = {
                            gridLineWidth: 0,
                            lineWidth: 0,
                            visible: false,
                            labels: { enabled: false }                            
                        };
                        /**
                         * Y轴配置优化：添加区间散点数量统计显示
                         * @description 在y轴刻度中间显示每个区间的散点数量
                         * @feature 动态计算区间范围内的数据点数量并显示
                         * @performance O(n*m) - n为数据点数量，m为区间数量
                         */
                        // EXTENSION POINT: 预定义区间范围用于统计散点数量
                        const yAxisIntervals = [
                            { min: temp_result.result_hard_min, max: refValue, label: '基准区间' },
                            { min: refValue, max: refValue * 1.5, label: '1.5倍区间' },
                            { min: refValue * 1.5, max: refValue * 2, label: '2倍区间' },
                            { min: refValue * 2, max: refValue * 2.5, label: '2.5倍区间' },
                            { min: refValue * 2.5, max: temp_max, label: '超限区间' }
                        ];
                        if(temp_result.result_hard_min>0){
                            yAxisIntervals.unshift({ min: 0, max: temp_result.result_hard_min, label: '异常区间' });
                        }

                        /**
                         * 统计每个区间内的散点数量
                         * @param {Array} dataPoints - 所有数据点
                         * @param {Array} intervals - 区间定义
                         * @returns {Array} 包含数量统计的区间数组
                         */
                        const calculateIntervalCounts = (dataPoints, intervals) => {
                            return intervals.map((interval) => {
                                if (interval.max === temp_max) {
                                    return {
                                        ...interval,
                                        count: dataPoints.filter(point => point.val >= temp_max).length,
                                        maxPoint:interval.max,
                                        minPoint:interval.min
                                    };
                                }
                                // 标准区间处理：左闭右开区间 [min, max)
                                let count = dataPoints.filter(point =>
                                        point.val >= interval.min && point.val < interval.max
                                    ).length;

                                return {
                                    ...interval,
                                    count,
                                    maxPoint:interval.max,
                                    minPoint:interval.min
                                };
                            });
                        };

                        // OPTIMIZATION: 统计当前指标在各区间的散点数量
                        const intervalStats = calculateIntervalCounts(temp_arr, yAxisIntervals);
                        console.log(intervalStats);
                        /**
                         * Y轴刻度重新渲染：基于intervalStats数据生成自定义刻度标签
                         * @description 使用区间统计数据重新定义y轴刻度位置和标签显示
                         * @feature 启用useHTML支持富文本标签，显示区间统计信息
                         * @performance O(n) - n为区间数量
                         */
                        // EXTENSION POINT: 基于intervalStats生成自定义刻度位置
                        const customTickPositions = [
                            0, // 起始位置
                            temp_result.result_hard_min,
                            ...intervalStats.map(stat => stat.maxPoint),
                            temp_max // 最大值位置
                        ].sort((a, b) => a - b); // 确保刻度位置按升序排列

                        chartOptions.yAxis = {
                            title: { text: null },
                            gridLineWidth: 1,
                            // offset: -8,
                            min: 0,
                            // 添加指标值区间带（黄色表示硬指标范围，绿色表示软范围）
                            plotBands: [
                                { color: "#ffdc60", from: temp_result.result_hard_min, to: temp_result.result_hard_max, description: `标准(${temp_result.result_hard_min}~${temp_result.result_hard_max})` },
                                { color: "#9fe080", from: temp_result.result_soft_min, to: temp_result.result_soft_max, description: `标准(${temp_result.result_soft_min}~${temp_result.result_soft_max})` },
                            ],
                            plotLines: [
                                { width: 2, value: temp_result.result_hard_max + 1, color: 'red', dashStyle: 'shortdash' },
                            ],
                            // FEATURE: 使用intervalStats数据重新定义刻度位置
                            tickPositions: customTickPositions,
                            labels: {
                                useHTML: true, // 启用HTML渲染支持
                                align: 'center', // 标签居中对齐
                                verticalAlign: 'middle', // 垂直居中对齐
                                y: 0, // 垂直位置调整，确保与刻度线对齐
                                formatter: function () {
                                    // REQUIRE: 根据刻度值匹配对应的区间统计信息
                                    const currentValue = this.value;
                                    
                                    // 查找匹配的区间统计
                                    const matchedStat = intervalStats.find(stat =>currentValue>stat.minPoint &&  currentValue<=stat.maxPoint);
                                    console.log(matchedStat);
                                    if (matchedStat && matchedStat.count > 0) {
                                        // OPTIMIZATION: 显示区间标签和数量统计，数量显示在刻度值下方
                                        return `<div style="display: flex; flex-direction: column; align-items: center; line-height: 1.0; min-width: 40px; white-space: nowrap;">
                                                    <span style="color: ${currentValue === temp_max ? '#ff4444' : '#333'}; font-size: 11px;  margin-bottom: 2px;">
                                                        ${currentValue === temp_max ? '↑↑↑' : currentValue.toFixed(0)}
                                                    </span>
                                                   
                                                    <span style="color: #666; font-size: 8px; white-space: nowrap;margin: 5px 0;">(${matchedStat.count}个)</span>
                                                    
                                                </div>`;
                                                // <div style="width: 0.5px;height: 20px;background-color: #6666;"></div>
                                    }
                                    // 默认显示数值
                                    return `<span style="font-size: 11px; color: #666;">${currentValue.toFixed(0)}</span>`;
                                }
                            }
                        };
                       
                        // 处理指标数据，按值分组并创建散点图系列
                        // temp_arr包含当前选中指标的所有数据点
                        if (temp_arr?.length > 0) {
                            // 按指标值对数据进行分组，相同值的数据点会被分到同一组
                            let temp_group = groupBy(temp_arr, 'val');
                            /**
                             * 排序方法：数值大的排中间，小的放两边
                             * @description 将数值按照从中间向两边扩散的方式排序，实现视觉上的平衡分布
                             * @performance O(n log n) - 主要消耗在排序操作
                             * @optimization 预计算排序值和索引映射，避免重复计算
                             */
                            // OPTIMIZATION: 预计算排序后的数值数组和索引映射，避免在每次比较时重复计算
                            const sortedValues = Object.keys(temp_group)
                                .map(v => Number(v))
                                .sort((a, b) => a - b);

                            const midIndex = Math.floor(sortedValues.length / 2);

                            // 创建值到索引的映射表，提高查找效率 O(1)
                            const valueIndexMap = new Map();
                            sortedValues.forEach((value, index) => {
                                valueIndexMap.set(value, index);
                            });

                            let gkeys = Object.keys(temp_group).sort((a, b) => {
                                const numA = Number(a);
                                const numB = Number(b);

                                // PERFORMANCE: 使用预计算的映射表，避免重复的indexOf查找 O(1) vs O(n)
                                const indexA = valueIndexMap.get(numA);
                                const indexB = valueIndexMap.get(numB);

                                // 计算距离中间位置的距离，距离越小越靠中间
                                const distA = Math.abs(indexA - midIndex);
                                const distB = Math.abs(indexB - midIndex);

                                return distA - distB;
                            });

                          
                            // 定义倍数区间
                            const intervals = [
                                { min: 0, max: refValue , step: 1 },
                                { min: refValue * 1.5, max: refValue * 5, step: 10},
                                { min: refValue * 2, max: refValue * 10, step: 20,},
                                { min: refValue * 2.5, max: Infinity, step: 30}
                            ];
                            let temp_series = { type: "scatter", events: { click: this.scatterClick }, marker: { enabledThreshold: 1, radius: 1.2 }, data: [] ,color:'red',events:{
                                            click:function(e){
                                                window.parent.postMessage({
                                                    type: 'quota',
                                                    entobjdataid: e.point.options.entobjdataid,
                                                    quota_text:e.point.options.quota_text
                                                }, '*');
                                            }
                                        } };
                            let temp_x = 0;
                            for (let nk of gkeys) {
                                const val = Number(nk);
                                let interval = intervals.find(i => val >= i.min && val < i.max) || intervals[intervals.length - 1];
                                let temp_val= val>temp_max?temp_max:val;
                                let temp_data = temp_group[nk].map((s, i) => {
                                    return {
                                        x: temp_x + i * interval.step,
                                        y: temp_val,
                                        value: val,
                                        floorname: s.floorname,
                                        entobjdataid: s.entobjdataid,
                                        quota_text: s.quota_text
                                    }
                                });
                                temp_series.data.push(...temp_data);
                                temp_x += interval.step * temp_group[nk].length;
                            }
                            chartOptions.series = [temp_series];
                        }
                        console.log(chartOptions);
                    }
                    else if(qtype==2){
                        // 极地散点
                        chartOptions.chart.polar=true;
                        chartOptions.tooltip.formatter=function () {
                                let temp_title = '指标值';//this.series.name;
                                if (this.fixedValues) {
                                    temp_title = '非固定值';
                                }
                                else if (this.multiples) {
                                    temp_title = '非倍数';
                                }
                                else if (this.noKeyValue) {
                                    temp_title = '无数据';
                                }
                                return `楼层: ${this.floorname}<br/>${temp_title}: ${this.value}`;
                            };
                        chartOptions.xAxis= {
                            gridLineWidth: 0,
                            lineWidth: 0,
                            labels: { enabled: false }
                        };
                        chartOptions.yAxis= {
                            labels: { enabled: false },
                            gridLineWidth: 0,
                            offset: -8,
                            min: 0,
                            plotBands: [
                                {
                                    from: 0,
                                    to: 270,
                                    color: "#f112125e",
                                },
                                {
                                    from: 50,
                                    to: 200,
                                    color: "#ffdc60",
                                },
                                {
                                    from: 80,
                                    to: 150,
                                    color: "#9fe080",
                                },
                            ],
                            plotLines: [{
                                // label: { text: 'max', y: 4, },
                                value: 240,
                                width: 0.5,
                                //dashStyle: "dashdot"
                            }],
                        };
                        chartOptions.series= [];
                        // 计算最大值（硬指标的1.25倍）
                        let temp_max = temp_result.result_hard_max * 1.25;
                        // 更新图表显示区域配置
                        // 添加指标值区间带（红色表示超出范围，黄色表示软指标范围，绿色表示正常范围）
                        chartOptions.yAxis.plotBands[0].to = temp_max;
                        chartOptions.yAxis.plotBands[0].description = '异常';
                        chartOptions.yAxis.plotBands[1].from = temp_result.result_hard_min;
                        chartOptions.yAxis.plotBands[1].to = temp_result.result_hard_max;
                        chartOptions.yAxis.plotBands[1].description = `标准(${temp_result.result_hard_min}~${temp_result.result_hard_max})`;
                        chartOptions.yAxis.plotBands[2].from = temp_result.result_soft_min;
                        chartOptions.yAxis.plotBands[2].to = temp_result.result_soft_max;
                        chartOptions.yAxis.plotBands[2].description = `最佳(${temp_result.result_soft_min}~${temp_result.result_soft_max})`;
                        chartOptions.yAxis.plotLines[0].value = temp_max+1;
                        //刻度
                        chartOptions.yAxis.tickPositions = [0, temp_result.result_hard_min, temp_result.result_soft_min, temp_result.result_soft_max, temp_result.result_hard_max, temp_max];
                        // 重置图表数据系列
                        chartOptions.series = [];
                        // 处理指标数据，按值分组并创建散点图系列
                        // temp_arr包含当前选中指标的所有数据点
                        if (temp_arr?.length > 0) {
                            // 按指标值对数据进行分组，相同值的数据点会被分到同一组
                            let temp_group = groupBy(temp_arr, 'val');
                            let gkeys=Object.keys(temp_group).sort((a,b)=>Number(a)-Number(b));
                            let errorItems= Object.values(temp_group).map(v=>v[0]).filter(v=>v?.hard||v?.noKeyValue||v?.fixedValues||v?.multiples);
                            let xAvgRatio = 360 / errorItems.length;
                            let temp_xRatio = 0;
                            // 遍历每个不同的指标值
                            for (let nk of gkeys) {
                                // 确保该值组中有数据
                                if (temp_group[nk]?.length > 0) {
                                    // 获取该组的第一个数据点作为代表
                                    let temp_group_data = temp_group[nk][0];
                                    // 判断是否为异常值
                                    let isError = errorItems.find(v=>v.val==nk); //(temp_group_data?.hard || temp_group_data?.noKeyValue || temp_group_data?.fixedValues || temp_group_data?.multiples);
                                    // 计算极坐标图上的角度分布（360度平均分配）
                                    let normalXRatio = 360 / temp_group[nk].length;
                                    let temp_avg_xRatio = xAvgRatio / temp_group[nk].length;
                                    chartOptions.series.push({
                                        type: "scatter",
                                        events:{
                                            click:function(e){
                                                window.parent.postMessage({
                                                    type: 'quota',
                                                    entobjdataid: e.point.options.entobjdataid,
                                                    quota_text:e.point.options.quota_text
                                                }, '*');
                                            }
                                        },
                                       name:`${temp_group_data.val}(${temp_group[nk].length})`,
                                       data:temp_group[nk].map((s, i) => {
                                            return {
                                                floorname: temp_group_data.floorname,
                                                x: isError? (temp_xRatio+ ((i+1)*temp_avg_xRatio)): (i*normalXRatio),
                                                y: (Number(s.val) > temp_max || s.val == null) ? temp_max : Number(s.val),
                                                value: Number(s.val),
                                                // normal:true,
                                                entobjdataid:s.entobjdataid,
                                                quota_text:temp_group_data.quota_text,
                                                fixedValues: temp_group_data.fixedValues,
                                                multiples: temp_group_data.multiples,
                                                noKeyValue: temp_group_data.noKeyValue
                                            }
                                        }),
                                        // color: isNormal ? 'black' : 'red',
                                        marker: { enabledThreshold: 1, radius: isError? 2:1.2 },
                                        color :isError?'red':'black',
                                    });
                                    if(isError){
                                        temp_xRatio += xAvgRatio;
                                    }
                                }
                            }
                        }
                    }
                }
                return chartOptions;
            },
            setChartRef(el, quota_id,result_index,qtype) {
                if (el && quota_id && result_index>-1) {
                    let chartConfig = this.getChartConfig(quota_id,result_index,qtype);
                    Highcharts.chart(el, chartConfig);
                }
            },
            /**
             * 执行后基础信息表构件指标计算
             * @description 该方法用于处理和计算构件的指标数据
             * @async
             * @function houjquota
             * @returns {Promise<void>}
             * 
             * 主要功能：
             * 1. 清除现有图表数据
             * 2. 重置加载状态和数据结构
             * 3. 获取构件指标数据
             * 4. 处理指标树形结构
             * 5. 展示计算结果
             */
            async houjquota(userprojectid,buildingid,speciltyids,xgj_entnames,floornames,entobjdataids) {
                this.viewLoading=true;
                // 获取构件指标数据
                // this.allData = await HoujComponentQuota("\\\\************\\云服务器项目同步目录\\wwb-结构_12套测试识别\\整体测试\\114号清单及图纸（新） - 副本");
                this.allData = await HoujComponentQuota(userprojectid,buildingid,speciltyids,xgj_entnames,floornames,entobjdataids,this.hasHard);
    
                if (this.allData?.length > 0) {    
                    // 处理数据分组
                    this.allData.forEach(qd => {
                        qd.quotavalues2.forEach(qv => {
                            qv.qgroup = groupBy(qv.all_data, 'floorname');
                        });
                    });
                    this.showData=this.allData[0].quotavalues2;
                    if(this.showData?.length>0){
                        this.showData.forEach(d=>{
                            this.setChartRef(d.chart_el,d.quota_id,d.result_index,d.quota_type);
                        });
                    }
                } 
                this.viewLoading=false;
            }
        }
    }
    </script>
    
    <style scoped>
    .calc-tab{
        height: 492px;
        width: 500px;
    }
    .el-space{
        display:flex;
        transition: margin-left 0.3s ease;
    }
    :deep(.highcharts-subtitle span){
        display: inline-block;
        vertical-align: middle;
    }
    :deep(.highcharts-subtitle span.legend-box){
       width: 13px;
       height: 11px;
    }
    :deep(.highcharts-subtitle span+span.legend-box){
        margin-left: 5px;
    }
     :deep(.highcharts-subtitle span.legend-box+span){
        margin-left: 2px;
    }
    </style>