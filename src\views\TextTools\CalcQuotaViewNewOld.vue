<template>
    <!--
      构件指标计算视图组件
      功能：
      1. 显示用户和项目选择界面
      2. 展示构件指标树形结构
      3. 显示指标计算结果(包括图表和表格)
      4. 提供指标异常检测功能
    -->
        <el-row style="height:100%">
            <el-col :span="24">
                <el-row>
                    <el-form-item label="所属用户">
                        <el-select placeholder="选择用户" v-model="currentUserId" filterable 
                        :loading="projectLoading" @change="userIdchange" style="width: 400px;">
                            <el-option v-for="info in userinfos" :label="info.username" :value="info.userid" />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="项目">
                        <el-select placeholder="选择项目" clearable v-model="currentProjectId" filterable remote :remote-method="loadData"
                        :loading="projectLoading" @change="projectIdchange" style="width: 700px;">
                            <el-option v-for="pr in projectResults" :label="pr.userprojectName" :value="pr.userprojectid" :disabled="!pr.dbexists" />
                        </el-select>
                    </el-form-item>
                    <!-- <el-form-item label="按层统计">
                        <el-checkbox v-model="useFloor"/>
                        <el-button text type="primary" icon="Search" @click="houjquota" >查看指标结果</el-button>
                    </el-form-item> -->
                    <el-form-item >
                        <!-- <el-checkbox v-model="useFloor"/> -->
                        <el-button text type="primary" icon="Search" @click="houjquota" >查看指标结果</el-button>
                    </el-form-item>
                </el-row>
                <el-row :loading="allLoading">
                    <el-col :span="4">
                        <el-tree ref="qTree" :data="quotaTreeData" style="width: 100%;" highlight-current default-expand-all @current-change="treeChange" node-key="value">
                            <template #default="{node,data}">
                                <el-text>{{ node.label }}<el-text v-if="data.count" :type="data?.type==2&&data?.relationType==2? 'danger':''">({{ data.count }})</el-text></el-text>
                            </template>
                        </el-tree>
                    </el-col>
                    <el-col :span="20">
                        <el-row>
                            <el-col :span="14">
                                <el-card>
                                    <template #header>
                                        <span>{{ currentQuota?.title }}</span>
                                    </template>
                                    <el-descriptions :title="currentQuota?.content" size="small" :column="2" border :label-width="160">
                                        <el-descriptions-item label="硬指标">{{ `${currentQuotaReuslt?.result_hard_min??''}~${currentQuotaReuslt?.result_hard_max??''}` }}</el-descriptions-item>
                                        <el-descriptions-item label="软指标">{{ `${currentQuotaReuslt?.result_soft_min??''}~${currentQuotaReuslt?.result_soft_max??''}` }}</el-descriptions-item>
                                        <el-descriptions-item label="倍数">{{currentQuotaReuslt?.multiples}}</el-descriptions-item>
                                        <el-descriptions-item label="固定值">{{currentQuotaReuslt?.fixedValues?.join(',')}}</el-descriptions-item>
                                    </el-descriptions>
                                </el-card>
                                <div ref="hcharts" style="height: 860px;width: 100%;"></div>
                            </el-col>
                            <el-col :span="10">
                                <el-tabs v-model="currentTab"  type="border-card" >
                                    <el-tab-pane v-for="td in tabsData" :key="td.buildname" :name="td.buildname" :label="td.buildname" >
                                       <el-collapse v-model="currentCollapse" accordion>
                                        <el-collapse-item v-for="qgKey of Object.keys(td.qgroup)" :name="qgKey" :disabled="!(currentQuota.type==2 && currentQuota.relationType==2)" >
                                            <template #title>
                                                <b>楼层:</b><el-tag disable-transitions>{{ `${qgKey} (${currentQuota.type==2?(td.qgroup[qgKey]?.length??0):td.qgroup[qgKey][0].val})` }}</el-tag>
                                            </template>
                                            <vxe-table v-if="currentQuota.type==2 && currentQuota.relationType==2" ref="dTable" height="600" :data="td.qgroup[qgKey]" show-overflow stripe 
                                            border :column-config="{ resizable: true }"
                                            :row-config="{ useKey: true, isHover: true, isCurrent: true, keyField: 'entobjdataid', transform: true, height: '28' }"
                                            :scroll-y="{enabled:true,gt:0}">
                                            <vxe-column  title="构件" width="120" >
                                                <template #default="{row}">
                                                    <el-text v-if="currentQuota.type==1">{{ currentQuota.title }}</el-text>
                                                    <el-text v-else-if="currentQuota.type==2">{{ `${row.xgj_entname}(${row.component_name})`}}</el-text>
                                                </template>
                                            </vxe-column>
                                            <vxe-column field="zwwz" title="轴网位置" ></vxe-column>
                                            <vxe-column title="指标值" width="160" >
                                                <template #default="{ row }">
                                                    <el-text v-if="row.noKeyValue" type="danger">无数据</el-text>
                                                    <el-text v-else-if="row.multiples" type="danger">{{ `非倍数:${row.val}` }}</el-text>
                                                    <el-text v-else-if="row.fixedValues" type="danger">{{ `非固定值:${row.val}` }}</el-text>
                                                    <el-text v-else-if="row.hard" type="danger">{{ row.val }}</el-text>
                                                    <el-text v-else-if="row.soft" type="warning">{{ row.val }}</el-text>
                                                    <el-text v-else type="success">{{ row.val }}</el-text>
                                                </template>
                                            </vxe-column>
                                        </vxe-table>
                                        </el-collapse-item>
                                       </el-collapse>
                                    </el-tab-pane>
                                </el-tabs>
                            </el-col>
                        </el-row>
                    </el-col>
                </el-row>
            </el-col>
        </el-row>
    </template>
    
    <script>
    import { ref } from 'vue'
    import { VxeTable } from 'vxe-table'
    import Highcharts from 'highcharts'
    import HighchartsMore from 'highcharts/highcharts-more'
    import exporting from 'highcharts/modules/exporting'
    import { GetProjectBuilds,GetProjectResults,HoujExists,HoujComponentQuota,GetAllQuota,GetProjectUserInfo } from '@/api/quota_userProjectResult'
    import { ElMessage } from 'element-plus';
    import { groupBy } from 'lodash-es'
    import { GetGraphicalComponents } from '@/api/graphicalComponent'
    
    Highcharts.setOptions({lang:{locale:'zh-CN'}});
    
    export default {
        /**
         * 组件setup函数
         * 初始化：
         * 1. 表格组件引用
         * 2. 树形组件属性配置
         */
        setup() {
            const dTable = ref <VxeTable> ({});
            const treeProps = { children: "children", label: "label", value: "value" };
            return { dTable, treeProps };
        },
        data() {
            return {
                allLoading:false,
                userinfos:[],
                currentUserId:null,
                projectResults: [],
                projectBuilds: [],
                quotaData: [],
                currentProjectId: null,
                currentProjectDir: null,
                showData: [],
                allComponents: [],
                projectLoading: false,
                quotaTreeData: [],
                useFloor: false,
                allQuota: [],
                allData: [],
                tabsData:[],
                currentTab:null,
                currentCollapse:null,
                currentQuota:{},
                currentQuotaReuslt:{},
                hcahrts:null,
                // Highcharts图表配置
                chartOptions: {
                    // 关闭无障碍功能
                    // 关闭版权信息
                    accessibility: { enabled: false },
                    credits: { enabled: false },
                    chart: {
                        //极地坐标
                        polar: true,
                    },
                    legend: {
                        // align: "left",
                        // verticalAlign: "middle",
                        // layout: "vertical",
                        enabled:false
                    },
                    tooltip: {
                        formatter: function () {
                            let temp_title= '指标值';//this.series.name;
                            if(this.fixedValues){
                                temp_title='非固定值';
                            }
                            else if(this.multiples){
                                temp_title='非倍数';
                            }
                            else if(this.noKeyValue){
                                temp_title='无数据';
                            }
                            return `楼层: ${this.floorname}<br/>${temp_title}: ${this.value}`;
                        }
                    },
                    title: { text: null},
                    subtitle: { text: null},
                    xAxis: {
                        gridLineWidth: 0,
                        lineWidth: 0,
                        labels: { enabled: false }
                    },
                    yAxis: {
                        gridLineWidth: 0,
                        offset:-8,
                        min: 0,
                        plotBands: [
                            {
                                from: 0,
                                to: 270,
                                color: "#f112125e",
                            },
                            {
                                from: 50,
                                to: 200,
                                color: "#ffdc60",
                            },
                            {
                                from: 80,
                                to: 150,
                                color: "#9fe080",
                            },
                        ],
                        plotLines: [{
                            label:{ text:'max',y:4, },
                            value: 240,
                            width: 0.5,
                            //dashStyle: "dashdot"
                            }],
                        // tickPositions: [0, 50, 80, 150, 200, 240],
                    },
                    series: [
                        //  {
                        //     type: "scatter",
                        //     name: "正常值",
                        //     data: [],
                        //     color: 'black',// "#505050",
                        //     marker: { radius: 1.5 },
                        //     // visible:false
                        // },
                        // {
                        //     type: "scatter",
                        //     name: "异常值",
                        //     data: [],
                        //     color: "red",
                        //     marker: { radius: 2.8 },
                        //     clip: false
                        // },
                    ]
                }
            };
        },
        /**
         * 组件挂载后执行
         * 初始化数据：
         * 1. 获取所有指标定义
         * 2. 获取项目用户信息
         * 3. 获取所有图形构件
         */
        async mounted() {
            this.allQuota= await GetAllQuota();
            this.userinfos= await GetProjectUserInfo();
            this.allComponents=await GetGraphicalComponents();
         },
        methods: {
            /**
             * 用户选择变更处理
             * @param {string} val - 选中的用户ID
             * 功能：
             * 1. 根据用户ID加载相关项目
             * 2. 重置当前项目选择
             */
            async userIdchange(val){
                this.projectLoading=true;
                this.currentProjectId=null;
                this.projectResults= await GetProjectResults(val);
                this.projectLoading=false;
            },
            /**
             * 根据名称加载项目数据
             * @param {string} name - 项目名称（用于远程搜索）
             * 功能：
             * 1. 根据当前用户ID和项目名称远程搜索项目
             * 2. 更新项目列表
             * 3. 用于el-select组件的远程搜索功能
             */
            async loadData(name){
                this.projectLoading=true;
                this.projectResults= await GetProjectResults(this.currentUserId,name);
                this.projectLoading=false;
            },
            /**
             * 指标树节点选择变更处理
             * @param {Object} data - 选中的树节点数据
             * 实现步骤：
             * 1. 清除现有图表
             * 2. 获取选中指标的定义和结果
             * 3. 根据指标类型和关系类型处理数据
             * 4. 配置图表显示参数：
             *    - 设置指标值范围区域（硬指标、软指标）
             *    - 配置数据点展示（正常值、异常值）
             *    - 设置图表标记和颜色
             * 5. 重新渲染极坐标散点图
             */
            treeChange(data){
                this.clearHighCharts();
                this.tabsData = null;
                this.currentQuota = this.allQuota.find(d=>d.id==data.value);
                let temp_result = null;
                if( this.currentQuota){
                    let results = JSON.parse(this.currentQuota.jsonData)?.results;
                    if(results?.length>0)temp_result = results[data.result_index];
                }
                this.currentQuotaReuslt = temp_result;
                if(temp_result){
                    let temp_arr = this.allData.flatMap(a=>a.quotavalues2).filter(a=>a.quota_id==data.value).flatMap(a=>a.all_data);
                    this.tabsData = this.allData.map(d=>{return {buildname:d.buildname,qgroup:groupBy(d.quotavalues2.filter(a=>a.quota_id==data.value).flatMap(a=>a.all_data),'floorname')}});
                    
                    //标题
                    this.chartOptions.title.text = this.currentQuota.title;
                    this.chartOptions.subtitle.text = `标准值: ${this.currentQuotaReuslt?.result_hard_min??''}~${this.currentQuotaReuslt?.result_hard_max??''}    最佳值: ${this.currentQuotaReuslt?.result_soft_min??''}~${this.currentQuotaReuslt?.result_soft_max??''}`;
                    if(this.currentQuotaReuslt?.multiples){
                        this.chartOptions.subtitle.text += `  倍数: ${this.currentQuotaReuslt?.multiples}`;
                    }
                    if(this.currentQuotaReuslt?.fixedValues?.length>0){
                        this.chartOptions.subtitle.text += `<br/>固定值: ${this.currentQuotaReuslt?.fixedValues?.join(',')}`;
                    }
                    
                    // 计算最大值（硬指标的1.25倍）
                    let temp_max = temp_result.result_hard_max * 1.25;
                    // 更新图表显示区域配置
                    // 添加指标值区间带（红色表示超出范围，黄色表示软指标范围，绿色表示正常范围）
                    this.chartOptions.yAxis.plotBands[0].to = temp_max;
                    this.chartOptions.yAxis.plotBands[1].from = temp_result.result_hard_min;
                    this.chartOptions.yAxis.plotBands[1].to = temp_result.result_hard_max;
                    this.chartOptions.yAxis.plotBands[2].from = temp_result.result_soft_min;
                    this.chartOptions.yAxis.plotBands[2].to = temp_result.result_soft_max;
                    this.chartOptions.yAxis.plotLines[0].value = temp_max;
                    //刻度
                    this.chartOptions.yAxis.tickPositions = [0,temp_result.result_hard_min,temp_result.result_soft_min,temp_result.result_soft_max,temp_result.result_hard_max,temp_max];
    
                    // 重置图表数据系列
                    this.chartOptions.series = [];
                    // 处理指标数据，按值分组并创建散点图系列
                    // temp_arr包含当前选中指标的所有数据点
                    if(temp_arr?.length>0){
                        // 按指标值对数据进行分组，相同值的数据点会被分到同一组
                        let temp_group = groupBy(temp_arr,'val');
                        // 遍历每个不同的指标值
                        for(let nk of Object.keys(temp_group)){
                            // 确保该值组中有数据
                            if(temp_group[nk]?.length>0){
                                // 获取该组的第一个数据点作为代表
                                let temp_group_data=temp_group[nk][0];
                                // 判断是否为正常值：小于最大值且不存在异常标记
                                let isNormal=Number(nk)<temp_max && !(temp_group_data?.noKeyValue||temp_group_data?.fixedValues||temp_group_data?.multiples);
                                // 计算极坐标图上的角度分布（360度平均分配）
                                let xRatio= 360/temp_group[nk].length;
                                this.chartOptions.series.push({
                                    type: "scatter",
                                   // name: nk,//isNormal?"正常值":'异常值',
                                    data: isNormal? temp_group[nk].map((s,i)=>{return {
                                        floorname:temp_group_data.floorname,
                                        x:i*xRatio,
                                        y:(Number(s.val)>temp_max||s.val==null)?temp_max:Number(s.val),
                                        value:Number(s.val)}
                                    }):
                                    temp_group[nk].map((s,i)=>{return {
                                        floorname:temp_group_data.floorname,
                                        x:i*xRatio,
                                        y:(Number(s.val)>temp_max||s.val==null)?temp_max:Number(s.val),
                                        value:s.val,
                                        fixedValues:temp_group_data.fixedValues,
                                        multiples:temp_group_data.multiples,
                                        noKeyValue:temp_group_data.noKeyValue}
                                    }),
                                    color:isNormal?'black':'red',
                                    marker: {enabledThreshold:1, radius: isNormal?1.3: 2.3}, 
                                });
                            }
                        }
                    }
                    //正常数据散点
                    // let normalData=temp_arr;
                    // if( this.currentQuota.type==2 &&  this.currentQuota.relationType==2 )
                    // {
                    //     normalData= temp_arr.filter(s=>!(s?.noKeyValue||s?.fixedValues||s?.multiples));
                    // }
                    // this.chartOptions.series[0].data=normalData?.map(d=>{return {floorname:d.floorname,y:Number(d.val), value:Number(d.val)}});
                    //异常数据散点
                    //let errorData=temp_arr?.filter(s=>s?.noKeyValue||s?.fixedValues||s?.multiples);
                    //this.chartOptions.series[1].data=errorData?.map(s=>{return {floorname:s.floorname,y:(Number(s.val)>temp_max||s.val==null)?temp_max:Number(s.val),value:s.val,fixedValues:s.fixedValues,multiples:s.multiples,noKeyValue:s.noKeyValue}});
                    
                    //初始化hcharts
                    this.hcahrts = Highcharts.chart(this.$refs.hcharts, this.chartOptions);
                }
            },
           /**
            * 指标数据过滤器
            * @param {Object} d - 指标数据对象
            * @returns {boolean} - 是否为异常数据
            * 异常情况包括：
            * - 无数据(noKeyValue)
            * - 非固定值(fixedValues)
            * - 非倍数值(multiples)
            * - 超出硬指标范围(hard)
            * - 超出软指标范围(soft)
            * 用于筛选需要特殊处理或显示的异常指标数据
            */
            quotaDataFilter(d){
                return d?.noKeyValue == true || d?.fixedValues == true || d?.multiples == true||d?.hard == true||d?.soft == true;
            },
            /**
             * 执行后基础信息表构件指标计算
             * @description 该方法用于处理和计算构件的指标数据
             * @async
             * @function houjquota
             * @returns {Promise<void>}
             * 
             * 主要功能：
             * 1. 清除现有图表数据
             * 2. 重置加载状态和数据结构
             * 3. 获取构件指标数据
             * 4. 处理指标树形结构
             * 5. 展示计算结果
             */
            async houjquota(){
                // 清除现有图表
                this.clearHighCharts();
                // 设置加载状态
                this.allLoading = true;
                // 重置数据结构
                this.quotaTreeData = [];
                this.tabsData = [];
                // 获取构件指标数据
                this.allData = await HoujComponentQuota(this.currentProjectDir);
    
                if(this.allData?.length > 0){
                    // 展平数据结构并添加建筑名称
                    let temp_list = this.allData.flatMap(qr => qr.quotavalues2.map(d => {
                        d.buildname = qr.buildname;
                        return d;
                    }));
                    
                    // 关联指标定义数据
                    temp_list.forEach(qr => {
                        qr.quota = this.allQuota.find(q => q.id == qr.quota_id);
                    });
    
                    // 构建数据数组，包含指标、建筑名称、结果索引和数据
                    let distinctArrs = temp_list.map(d => {
                        return {
                            quota: d.quota,
                            buildname: d.buildname,
                            result_index: d.result_index,
                            arr: d.all_data
                        }
                    });
    
                    // 构建树形结构数据
                    let treeData = this.allQuota.map(d => {
                        let temp_bcom = JSON.parse(d.jsonData)?.bcomsa?.at(0);
                        return {
                            label: d.title,
                            value: d.id,
                            count: 0,
                            type: d.type,
                            relationType: d.relationType,
                            specialty: temp_bcom?.bcomIds?.at(0),
                            bcomid: temp_bcom?.bcomIds?.at(1),
                            bcom: temp_bcom?.bcom
                        }
                    });
    
                    console.log(treeData);
    
                    // 计算每个指标的统计数据
                    for (let index = 0; index < distinctArrs.length; index++) {
                        let temp_data = treeData.find(d => d.value == distinctArrs[index].quota.id);
                        let tempArrCount = 0;
                        
                        if(distinctArrs[index].arr?.length > 0){
                            // 根据指标类型和关系类型计算统计值
                            if(distinctArrs[index].quota.type == 1 && distinctArrs[index].quota.relationType == 2){
                                // 类型1关系2：累加值
                                tempArrCount = distinctArrs[index].arr.map(d => Number(d.val))?.reduce((p,c) => p + c);
                            } else {
                                // 其他类型：异常数据计数
                                tempArrCount = distinctArrs[index].arr?.filter(this.quotaDataFilter)?.length ?? 0;
                            }
                        }
    
                        if(temp_data){
                            temp_data.count += tempArrCount;
                            temp_data.result_index = distinctArrs[index].result_index;
                        }
                    }
    
                    // 构建最终的树形结构
                    this.quotaTreeData = this.allComponents
                        .filter(d => treeData.find(t => t.specialty == d.id))
                        .map(d => {
                            let temp_data = {
                                label: d.title,
                                value: d.id
                            };
                            temp_data.children = d.children
                                .filter(c => treeData.find(t => t.bcomid == c.id))
                                .map(c => {
                                    return {
                                        label: c.title,
                                        value: c.id,
                                        children: treeData.filter(td => td.bcomid == c.id)
                                    }
                                });
                            return temp_data;
                        });
    
                    console.log(this.quotaTreeData);
    
                    // 处理数据分组
                    this.allData.forEach(qd => {
                        qd.arr = qd.quotavalues2.flatMap(d => d.all_data);
                        qd.qgroup = groupBy(qd.arr, 'floorname');
                    });
    
                    // 设置默认选中项
                    let defaultTreeKey = this.quotaTreeData[0]?.children?.at(0)?.value;
                    await this.$refs.qTree.setCurrentKey(defaultTreeKey);
    
                    // 构建标签页数据
                    this.tabsData = this.allData.map(d => {
                        return {
                            buildname: d.buildname,
                            qgroup: groupBy(
                                d.quotavalues2
                                    .filter(a => a.quota_id == defaultTreeKey)
                                    .flatMap(a => a.all_data),
                                'floorname'
                            )
                        }
                    });
    
                    // 设置默认标签页
                    let tempTabData = this.tabsData?.at(0);
                    if(tempTabData){
                        this.currentTab = tempTabData.buildname;
                    }
                } else {
                    // 无异常数据时显示提示
                    await ElMessage({ 
                        type: 'success', 
                        message: '未检测到指标异常!' 
                    });
                }
                // 重置加载状态
                this.allLoading = false;
            },
            /**
            * 项目变更 记录项目地址
            */
            async projectIdchange(val){
                let tempProject= this.projectResults?.find(d=>d.userprojectid==val);
                this.currentProjectDir=tempProject?.workspace_nas;
            },
            /**
             * 清理hcharts
             */
            clearHighCharts(){
                if(this.hcahrts){
                    if( this?.hcahrts?.destory)this?.hcahrts?.destory();
                    this.$refs.hcharts.innerText='';
                }
            }
        },
    }
    </script>
    
    <style scoped>
    /**
     * 构件头部样式
     * 使用浅色背景和固定高度的容器
     */
    :deep(.el-header){
        background-color: var(--el-color-info-light-9);
        height: 40px;
    }

    
    /**
     * 错误列样式
     * 使用Element Plus的文本颜色变量实现与主题一致的展示
     */
    .error-col{
        color:var(--el-text-color);
    }
    
    /**
     * 树形控件当前节点高亮样式
     * 使用:deep选择器处理组件内部元素
     * 使用Element Plus的浅色主题色作为背景色
     */
    :deep(.el-tree--highlight-current .el-tree-node.is-current>.el-tree-node__content){
        background-color: var(--el-color-primary-light-7);
    }
    </style>
    