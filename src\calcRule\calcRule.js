/**
 * 元素
 */
export class calcRuleElement{
    /**
     * 
     * @param {Number} 类型 
     * @param {String} 构件
     * @param {String} 属性
     * @param {String} 数值
     * @param {Boolean} 空间范围内/外 
     * @param {String} 数据范围起始
     * @param {String} 数据范围结束 
     * @param {Number} 侵入量
     */
    constructor(elType,gj,sx,sz,cover,rang1,rang2,qzType){
        /**
         * 元素类型
         * 0 构件
         * 1 构件.属性
         * 2 数值 数值单位
         * 3 空间范围
         * 4 数值范围
         * 5 侵入增加量
         */
        this.elType=elType;
        /**
         * 构件
         */
        this.gj=gj;
        /**
         * 属性
         */
        this.sx=sx;
        /**
         * 数值
         */
        this.sz=sz;
        /**
         * 空间范围
         * 与sz使用
         * true 以内
         * false 以外
         */
        this.cover=cover;
        /**
         * 数值范围
         * 起始值
         */
        this.rang1=rang1;
        /**
         * 数值范围
         * 结束值
         */
        this.rang2=rang2;
        /**
         * 侵入量
         * 0 不计算
         * 1 侵入至中心线
         * 2 侵入至轴线
         * 3 侵入总长
         */
        this.qzType=qzType;
    }
}

/**
 * 规则数据
 */
export class calcRuleRow{
    /**
     * @param {String} id (guid)
     * @param {String} parentId (guid)
     * @param {String} groupid 规则id (guid)
     * @param {String} cg_id 专业id 
     * @param {String} mainType 规则类型 
     * @param {Number} calcType 计算类型 
     * @param {String} type 类型 
     * @param {String} tType 目标类型 
     * @param {Object} target 目标 
     * @param {Array} conds 条件
     * @param {String} ftype 公式类型
     * @param {calcRuleFormula} formula 公式内容
     * @param {String} classify 分类
     * @param {Object} source 公式(源)属性
     */
    constructor(id,parentId,groupid,cg_id,mainType,calcType,type,tType,target,conds,ftype,formula,classify){
        this.id=id;
        this.parentId=parentId;
        /**
         * 规则id
         */
        this.groupid=groupid;
        /**
         * 类型
         * gjgcl 工程量
         * br 并入
         * kc 扣除
         * gs 公式
         */
        this.type=type;
        /**
         * 专业id
         */
        this.cg_id=cg_id;
        /**
         * 规则类型
        * gjgcl 工程量
         * br 并入
         * kc 扣除
         * de 定额说明
         */
        this.mainType=mainType;
        /**
         * 计算类型
         * 1 体积
         * 2 面积
         * 3 长度
         * 4 数量
         * 5 质量
         */
        this.calcType=calcType;
        /**
         * 条件
         */
        this.conds=conds;
        /**
         * 目标类型
         */
        this.tType=tType;
        /**
         * 目标
         */
        this.target=target;
        /**
         * 公式类型
         */
        this.ftype=ftype;
        /**
         * 公式内容
         */
        this.formula=formula;
        /**
         * 分类
         */
        this.classify=classify;
    }
}

/**
 * 构件信息
 */
export class calcRuleBcoms{
    /**
     * 构件信息
     * @param {String} 构件id 
     * @param {String} 构件名称 
     */
    constructor(cg_id,cg_name){
        /**
         * 构件id
         */
        this.cg_id=cg_id;
        /**
         * 构件名称
         */
        this.cg_name=cg_name;
    }
}

/**
 * 条件
 */
export class calcRuleCond{
    /**
     * 
     * @param {calcRuleElement} 当前元素 
     * @param {calcRuleElement} 目标元素 
     * @param {String} 条件值 
     */
    constructor(source,target,val){
        /**
         * 当前元素
         */
        this.source=source;
        /**
         * 目标元素
         */
        this.target=target;
        /**
         * 条件值
         */
        this.val=val;
    }
}

/**
 * 公式
 */
 export class calcRuleFormula{
    /**
     * 
     * @param {String} 类型 
     * @param {Array} 参数
     */
    constructor(ftype,params){
        /**
         * 类型
         */
        this.ftype=ftype;
        /**
         * 参数
         */
        this.params=params;
    }
}

/**
 * 连接线数据
 */
 export class calcRuleLineData{
    /**
     * @param {String} 属性 
     * @param {Number} 属性下标
     */
    constructor(source,sourceIndex){
        /**
         * 属性
         */
        this.source=source;
        /**
         * 属性下标
         */
        this.sourceIndex=sourceIndex;
    }
}