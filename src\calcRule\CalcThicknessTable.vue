<template>
    <vxe-table border size="mini" :data="tbData" :row-config="{height:28}" show-overflow :edit-config="{ trigger: 'click', mode: 'row' }" >
        <vxe-column field="thickness" title="设计厚度" :edit-render="{ autofocus: '.vxe-input--inner'}" >
            <template #edit="{ row }">
                <vxe-input size="mini" type="number" v-model="row.thickness" ></vxe-input>
            </template>
        </vxe-column>
        <vxe-column field="calc_thickness" title="计算厚度" :edit-render="{ autofocus: '.vxe-input--inner'}">
            <template #edit="{ row }">
                <vxe-input type="number" size="mini" v-model="row.calc_thickness" ></vxe-input>
            </template>
        </vxe-column>
        <vxe-column title="操作" width="50">
            <template #default="{ $rowIndex }">
                <el-button size="small" text type="danger" icon="Delete"  @click="delData($rowIndex)"></el-button>
            </template>
        </vxe-column>
        </vxe-table>
</template>

<script>
import { ref } from 'vue'
import {  VxeTable } from 'vxe-table';
export default {
    name: "calcThicknessTable",
    props: {
        sData:Array,
        delData:Function,
    },
    setup() {
        const ctTable = ref<VxeTable>({});
        return { ctTable  }
    },
    data() {
        return {
            tbData: this.sData
        }
    },
}
</script>