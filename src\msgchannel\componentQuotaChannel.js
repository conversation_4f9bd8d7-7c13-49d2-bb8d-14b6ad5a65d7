const quotaMsgChannel= new BroadcastChannel('component_quota');

export const sendMsg=(type,content)=>{
    quotaMsgChannel.postMessage({type,content});
}

export const listenMsg=(callback)=>{
    const handler =(e)=>{
        callback&&callback(e.data);
    }
    quotaMsgChannel.addEventListener('component_message',handler);
    return ()=>{
        quotaMsgChannel.removeEventListener('component_message',handler);
        quotaMsgChannel.close();
    }
}