import axios from "axios"

/**
 * 根据构件分类查询
 * @returns 
 */
export function QueryByClassifications(series,classifications){
    return axios.post(`/BidCalcRelation/QueryByClassifications`,{id:series,val:classifications})
        .then((res) => {
            return res.data.data;
        })
}
/**
 * 获取规则关联地区
 * @returns 
 */
export function GetAllSeries(series,classifications){
    return axios.post(`/BidCalcRelation/GetAllSeries`,{id:series,val:classifications})
        .then((res) => {
            return res.data.data;
        })
}

/**
 * 保存数据
 * @param {Object} data 
 * @returns 
 */
export function SaveAsync(data){
    return axios.post(`/BidCalcRelation/Save`, data)
    .then((res) => {
        return  res?.data?.succeeded;
    })
}

/**
 * 删除数据
 * @param {Object} data 
 * @returns 
 */
 export function DeleteAsync(data){
    return axios.post(`/BidCalcRelation/Delete`, data)
    .then((res) => {
        return  res?.data?.succeeded;
    })
}

/**
 * 查重
 * @param {Object} data 
 * @returns 
 */
 export function ExistsAsync(data){
    return axios.post(`/BidCalcRelation/Exists`, data)
    .then((res) => {
        return  res?.data?.data;
    })
}

/**
 * 获取所有地区的构件数量
 * @returns 
 */
 export function QueryAreaCount(){
    return axios.get(`/BidCalcRelation/QueryAreaCount`)
    .then((res) => {
        return  res?.data?.data;
    })
}


/**
 * 按地区复制构件数据
 * @param {Object} data 
 * @returns 
 */
 export function TransferData(data){
    return axios.post(`/BidCalcRelation/TransferData`, data)
    .then((res) => {
        return  res?.data?.data;
    })
}

/**
 * 更新排序
 * @param {Array} ids
 * @returns 
 */
 export function UpdateSerial(ids){
    return axios.post(`/BidCalcRelation/UpdateSerial`, {items:ids})
    .then((res) => {
        return res?.data?.succeeded;
    })
}

/**
 * 获取构件分类
 * @returns 
 */
 export function QueryClassifications(){
    return axios.get(`/BidCalcRelation/QueryClassifications`)
    .then((res) => {
        return  res?.data?.data;
    })
}


/**
 * 获取计算规则构件
 * @returns 
 */
 export function QueryCalcBCom(){
    return axios.get(`/BidCalcRelation/GetBidCalcBCom`)
    .then((res) => {
        return  res?.data?.data;
    })
}


/**
 * 复制构件数据
 * @returns 
 */
 export function CopyRowData(bcom,newBCom,newClassification){
    return axios.post(`/BidCalcRelation/CopyRowData`,{bcom:bcom,newBCom:newBCom,newClassification:newClassification})
    .then((res) => {
        return  res?.data?.succeeded;
    })
}


/**
 * 获取关联地区
 * @returns 
 */
 export function GetBidCalcRelationArea(area,series){
    return axios.get(`/BidCalcRelation/GetBidCalcRelationArea?area=${area}&series=${series}`)
    .then((res) => {
        return  res?.data?.data;
    })
}

/**
 * 保存单个地区关联构件数据
 * @returns 
 */
 export function SaveAllBidCalcRelationArea(area,series,ids,cids){
    return axios.post(`/BidCalcRelation/SaveAllBidCalcRelationArea`,{area:area,series:series,ids:ids,classifications:cids})
    .then((res) => {
        return  res?.data?.succeeded;
    })
}

/**
 * 保存单个构件关联地区
 * @returns 
 */
 export function SaveBidCalcRelationAreas(id,vals){
    return axios.post(`/BidCalcRelation/SaveBidCalcRelationAreas`,{id:id,val:vals})
    .then((res) => {
        return  res?.data?.succeeded;
    })
}


/**
 * 关联多个规则到同一地区
 * @returns 
 */
 export function SaveBidCalcRelationAreasBySeries(series,vals){
    return axios.post(`/BidCalcRelation/SaveBidCalcRelationAreasBySeries`,{id:series,val:vals})
    .then((res) => {
        return  res?.data?.succeeded;
    })
}


/**
 * 从同一地区删除多个规则
 * @returns 
 */
 export function DeleteBidCalcRelationAreasBySeries(series,vals){
    return axios.post(`/BidCalcRelation/DeleteBidCalcRelationAreasBySeries`,{id:series,val:vals})
    .then((res) => {
        return  res?.data?.succeeded;
    })
}

/**
 * 地区锁
 * @returns 
 */
 export function GetLocked(area){
    return axios.get(`/BidCalcRelation/GetLocked?area=${area}`)
    .then((res) => {
        return  res?.data?.data;
    })
}

/**
 * 修改是否锁
 * @returns 
 */
 export function ChangeLock(area,locked,password){
    return axios.post(`/BidCalcRelation/ChangeLock`,{area:area,locked:locked,password:password})
    .then((res) => {
        return  res?.data?.succeeded;
    })
}

/**
 * 查询密码
 * @returns 
 */
 export function LockCheck(area,password){
    return axios.post(`/BidCalcRelation/LockCheck`,{area:area,password:password})
    .then((res) => {
        return  res?.data?.data;
    })
}
