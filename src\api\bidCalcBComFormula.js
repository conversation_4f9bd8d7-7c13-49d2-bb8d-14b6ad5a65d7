import axios from "axios"

/**
 * 根据构件分类查询
 * @returns 
 */
export function QueryByClassifications(series,classifications){
    return axios.post(`/BidCalcBComFormula/QueryByClassifications`,{id:series,val:classifications})
        .then((res) => {
            return res.data.data;
        })
}

/**
 * 保存数据
 * @param {Object} data 
 * @returns 
 */
export function SaveAsync(data){
    return axios.post(`/BidCalcBComFormula/Save`, data)
    .then((res) => {
        return  res?.data?.succeeded;
    })
}

/**
 * 删除数据
 * @param {Object} data 
 * @returns 
 */
 export function DeleteAsync(data){
    return axios.post(`/BidCalcBComFormula/Delete`, data)
    .then((res) => {
        return  res?.data?.succeeded;
    })
}

/**
 * 查重
 * @param {Object} data 
 * @returns 
 */
 export function ExistsAsync(data){
    return axios.post(`/BidCalcBComFormula/Exists`, data)
    .then((res) => {
        return  res?.data?.data;
    })
}

