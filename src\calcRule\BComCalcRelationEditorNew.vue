<template>
        <el-form label-width="100px" >
            <el-form-item label="计算规则构件" class="cond-tr">
                <el-cascader class="bcoms-cascader" v-model="currentBComs" :props="cprops" placeholder="构件选择" :options="bcomOptions"
                filterable clearable collapse-tags collapse-tags-tooltip  @change="bcomChange" >
                <template #default="{data}">
                        <el-text v-if="data.conds?.length>0" type="success" >{{ data.label }}</el-text>
                        <span v-else>{{ data.label }}</span>
                    </template>
                </el-cascader>
                <el-text class="deduct-text">条件</el-text>
                <bcomSortCondEditor v-for="(acond, aindex)  in currentRow.attr_conds" :cond="acond" :delCond="()=>delCond(currentRow.attr_conds,aindex)" :isDeductCond="false"   />
                <el-popover placement="right" trigger="click" >
                    <template #reference>
                        <el-button size="small" icon="plus" circle type="primary" class="btn-plus"></el-button>
                    </template>
                    <el-select size="small" @change="(op)=>addCond(currentRow,'attr_conds',op)" placeholder="选择条件" >
                        <el-option v-for="item in condOptions" :key="item.value" :label="item.label" :value="item.value" />
                        
                    </el-select>
                </el-popover>
            </el-form-item>
            
            <el-form-item label="目标构件" class="cond-tr">
                <el-cascader ref="targetBComCascader" class="bcoms-cascader" v-model="currentTargetBComs" placeholder="构件选择" :options="bcomOptions"
                filterable clearable collapse-tags collapse-tags-tooltip @change="targetBcomChange" >
                <template #default="{data}">
                    <el-text v-if="data.conds?.length>0" type="success" >{{ data.label }}</el-text>
                    <span v-else>{{ data.label }}</span>
                </template>
                </el-cascader>
                <el-text class="deduct-text">条件</el-text>
                <bcomSortCondEditor v-for="(tacond, taindex) in currentRow.target_attr_conds" :cond="tacond" :delCond="()=>delCond(currentRow.target_attr_conds,taindex)" :isDeductCond="false" />
                <el-popover placement="right" trigger="click" >
                    <template #reference>
                        <el-button size="small" icon="plus" circle type="primary" class="btn-plus"></el-button>
                    </template>
                    <el-select size="small" @change="(op)=>addCond(currentRow,'target_attr_conds',op)" placeholder="选择条件" >
                        <el-option v-for="item in condOptions" :key="item.value" :label="item.label" :value="item.value" />
                    </el-select>
                </el-popover>
            </el-form-item>

            <el-form-item label="扣减规则" >
                <el-tabs v-model="editableTab" type="border-card" addable @tab-add="handleTabsAdd" class="bcom-tabs">
                    <el-tab-pane v-for="(ded,index) in currentRow.deduct_settings" :key="ded" :name="index" >
                        <template #label>
                            <Rank class="i-rank" /><el-text>{{ bidAttributesMap.get(ded.attr) }}</el-text> <el-icon class="is-icon-close" ><Close @click.stop="showTabDelConfirm($event.target, ()=>delTabs(index))" /></el-icon>
                        </template>
                        <el-form label-width="120px" >
                            <el-row>
                                <el-col :span="12">
                                    <el-form-item label="扣减属性">
                                        <el-select class="ew" placeholder="属性" v-model="ded.attr" filterable remote remote-show-suffix :remote-method="attributesLoad" :loading="atLoading" >
                                            <el-option v-for="attr in localAttributes" :label="attr.name" :value="attr.id" />
                                        </el-select>
                                    </el-form-item>
                                </el-col>
                                <el-col :span="12">
                                    <el-form-item label="扣减选项">
                                        <el-select v-model="ded.operation" placeholder="扣减选项" style="width:300px">
                                            <el-option v-for="item in deductOptions" :key="item.value" :label="item.value==5||item.value==6?(item.label+' '+(getDeductLabel()??'')):item.label" :value="item.value" />
                                        </el-select>
                                    </el-form-item>
                                </el-col>
                            </el-row>
                            <el-row >
                                <el-form-item label="扣减条件">
                                    <bcomSortCondEditor v-for="(acond, aindex) in ded.deduct_conds" :cond="acond" :delCond="()=>delCond(ded.deduct_conds,aindex)" :isDeductCond="true" /> 
                                    <el-popover placement="right" trigger="click" >
                                        <template #reference>
                                            <el-button size="small" icon="plus" circle type="primary" class="btn-plus"></el-button>
                                        </template>
                                        <el-select size="small" @change="(op)=>addCond(ded,'deduct_conds',op)" placeholder="选择条件" >
                                            <el-option v-for="item in condOptions" :key="item.value" :label="item.label" :value="item.value" />
                                            <el-option key="3-1" label="扣减部位" value="3-1" />
                                            <el-option key="4-1" label="土方扣除条件" value="4-1" />
                                        </el-select>
                                    </el-popover>
                                </el-form-item>
                            </el-row>

                        </el-form>
                    </el-tab-pane>
                </el-tabs>
                <el-popover width="220" title="是否删除?" :visible="delConfirmShow" placement="bottom-end" :offset="22" :virtual-ref="delConfirmRef" virtual-triggering :persistent="false"  >
                    <div style="text-align: right;">
                        <el-button type="primary" size="small" @click="()=>{condDefFunc();delConfirmShow=false;}">确定</el-button>
                        <el-button size="small" @click="delConfirmShow=false">取消</el-button>
                    </div>
                </el-popover>
            </el-form-item>

            <el-form-item label="描述">
                <el-input type="textarea" v-model="currentRow.remarks" style="width: 70%;"></el-input>
            </el-form-item>

            <el-form-item>
                <el-button type="primary" @click="saveData(currentRow)">{{ this.currentRow.id ? '保存' : '新增' }}</el-button>
                <el-button @click="editCancel">取消</el-button>
            </el-form-item>
        </el-form>
</template>
<script>
import bcomSortCondEditor from './BComSortCondEditor.vue'
import {condOptions,deductOptions,ruleTypes,addCond,delCond} from '@/calcRule/calc_config'


export default {
    name: 'bComCalcRelationEditorNew',
    inject:['bidAttributes','bidAttributesMap','bcomOptions','calcBComs'],
    components: { bcomSortCondEditor },
    props: {
        saveData: Function,
        editCancel: Function
    },
    setup() {
        const cprops = { multiple: true};
        return { cprops,condOptions,deductOptions,ruleTypes,addCond,delCond }
    },
    data() {
        return {
            currentRow: {},
            editableTab:0,
            delConfirmShow:false,
            delConfirmRef:null,
            condDefFunc:null,
            localAttributes:null,
            atLoading:false,
            /**
             * 当前构件
             */
            currentBComs:null,
            /**
             * 目标构件
             */
            currentTargetBComs:null,
        }
    },
    methods: {
        /**
         * 加载属性
         */
        attributesLoad(keyword) {
            this.atLoading = true;
            if (keyword) {
                this.localAttributes = this.bidAttributes.filter(c => c.name.toLowerCase().includes(keyword.toLowerCase())).sort(this.lengtSort);
            }
            else {
                this.localAttributes =  this.bidAttributes;// this.cond.attr?this.bidAttributes.filter(c=>c.id==this.cond.attr):[]; 
            }
            this.atLoading = false;
        },
        /**
         * 修改当前显示数据
         * @param {object} row 
         */
         async changeData(row){
            this.currentRow=row;
            if(!this.currentRow.deduct_settings)this.currentRow.deduct_settings=[];
            this.currentBComs=row.bcom?.split(',')?.map(d=>[row.classification,Number(d)]);
            let temp_bcom=this.calcBComs.find(d=>d.id==Number(row.target_bcom));
            if(temp_bcom){
                this.currentTargetBComs=[temp_bcom.classification_id, temp_bcom.id];
            }
            else{
                this.currentTargetBComs=[];
            }
            await this.attributesLoad();
        },  
        bcomChange(){
            if(this.currentBComs?.length>0){
                this.currentRow.classification=this.currentBComs[0][0];
                let temp_bcoms= this.currentBComs.flatMap(d=>d[1]);
                this.currentRow.bcom=temp_bcoms.join(',');
            }
            else{
                this.currentRow.classification=null;
                this.currentRow.bcom=null;
            }
        },
        targetBcomChange(){
            if(this.currentTargetBComs?.length>1){
                this.currentRow.target_bcom=this.currentTargetBComs[1].toString();
            }
            else{
                this.currentRow.target_bcom=null;
            }
        },
        condTypeChange(ded,val){
            switch (val) {
                //扣减
                case 1:
                    ded={cond_type:1,operation:1,deduct_type:1,attr_conds:[]};
                    break;
                //属性转换
                case 2:
                    ded={cond_type:2,attr_conds:[],base_attr:null,trans_attr:null,factor:null};
                    break;
                //延伸
                case 3:
                    ded={cond_type:3,attr_conds:[],extension:null};
                    break;
                default:
                    break;
            }
        },
        getDeductLabel(){
            if(this.currentTargetBComs)return this.$refs.targetBComCascader.getCheckedNodes()[0]?.label;
        },
        /**
         * 弹出删除确认框
         * @param {Object} btn 
         * @param {Function} delFunc 
         */
         showTabDelConfirm(btn,delFunc){
            this.delConfirmRef=btn;
            this.delConfirmShow=true;
            this.condDefFunc=delFunc;
        },
        /**
         * 添加新tab
         */
        handleTabsAdd(){
            if(this.currentRow?.deduct_settings==undefined ||this.currentRow?.deduct_settings==null)this.currentRow.deduct_settings=[];
            this.currentRow.deduct_settings.push({cond_type:1,attr_conds:[],attr:null,factor:null});
            this.editableTab=this.currentRow.deduct_settings.length-1;
        },
         /**
         * 拖动
         */
         async tabsDrop() {
            this.sortable=Sortable.create(
                document.querySelector('.bcom-tabs .el-tabs__nav'),
                {
                    handle:'.i-rank',
                    animation:150,
                    onEnd:({newIndex,oldIndex})=>this.changeTabsOrder(newIndex,oldIndex)
                }
            );
        },
        /**
         * 修改tabs排序
         * @param {Number} newIndex 
         * @param {Number} oldIndex 
         */
        changeTabsOrder(newIndex,oldIndex){
            if(newIndex==oldIndex)return false;
            this.currentRow.deduct_settings.splice(newIndex,0,this.currentRow.deduct_settings.splice(oldIndex,1)[0]);
            this.editableTab=newIndex;
        },
        /**
         * 删除tab
         * @param {Number} tabIndex 
         */
        delTabs(tabIndex){
            this.currentRow.deduct_settings.splice(tabIndex,1);
            if(this.editableTab==tabIndex&&tabIndex>=this.currentRow.deduct_settings.length-1){
                this.editableTab=this.currentRow.deduct_settings.length-1;
            }
        },
    },
}
</script>
<style scoped>
.btn-plus{
    margin-left: 5px;
}
.deduct-text{
    margin-left: 20px;
    margin-right: 10px;
}
:deep(.bcoms-cascader){
    width: 300px;
}
.cond-tr{
    padding:4px;
    border:1px dashed var(--el-border-color);
    border-radius: 4px;
}
</style>

