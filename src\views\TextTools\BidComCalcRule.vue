<template>
    <el-row style="height:100%">
        <el-col :span="24">
            <el-affix :offset="0">
                <el-row class="bcom-header">
                    <el-row class="title">
                        <el-text size="large" tag="b">构件计算规则</el-text>
                    </el-row>
                </el-row>
                <el-row class="bcom-choose">
                    <el-col :span="16">
                        <el-text style="width: 50px;">地区</el-text>
                        <el-radio-group v-model="currentArea" @change="areaChange">
                            <el-radio v-for="area in allAreas" :label="area" border>{{ area }}</el-radio>
                        </el-radio-group>
                    </el-col>
                    <el-col :span="8">
                        <div class="tabs-buttons">
                            <el-button type="primary" @click="showEditor($event.target, {bcom: null, attr_conds: [], target_bcom, target_attr_conds: [],classification: null, serial: this.currentRowSerial, remarks: null})">添加构件</el-button>
                        </div>
                    </el-col>
                </el-row>
                <el-row class="bcom-choose">
                    <el-col :span="6">
                        <el-text>系列</el-text>
                        <el-select v-model="currentSeries" placeholder="系列" filterable :reserve-keyword="false" allow-create @change="loadSortData" style="width: 520px;">
                            <el-option v-for="item in areaSeries" :label="item.series ?? '未命名(空)'" :value="item.id" />
                        </el-select>
                    </el-col>
                    <el-col :span="6">
                        <el-text>构件</el-text>
                        <el-cascader v-model="currentClassification" :props="cprops" placeholder="构件选择" :options="bcomOptions" 
                        filterable clearable collapse-tags collapse-tags-tooltip @change="loadSortData" :teleported="false">
                        <template #default="{node,data}">
                            <el-text v-if="data.conds?.length>0" type="success" >{{ data.label }}</el-text>
                            <span v-else>{{ data.label }}</span>
                        </template>
                        </el-cascader>
                        <el-checkbox style="margin-left: 10px;" v-model="showCurrentArea" @change="loadSortData" >只显示当前地区</el-checkbox>
                    </el-col>
                    <el-col :span="8">
                        <el-button text icon="Operation" @click="formulaTableShow = !formulaTableShow">计算公式</el-button>
                        <el-button text icon="Setting" @click="thicknessTableShow = !thicknessTableShow">砌体厚度配置</el-button>
                    </el-col>
                    <!-- <el-col :span="4">
                        <el-button type="primary" icon="Check" @click="saveRelationArea" >关联地区</el-button>
                    </el-col> -->
                    <el-dialog v-if="formulaTableShow" v-model="formulaTableShow" title="计算公式" width="60%" center align-center destory-on-close @closed="loadFormulas">
                            <bComCalcFormulaTable :ftype="1" />
                     </el-dialog>
                     <el-dialog v-if="thicknessTableShow" v-model="thicknessTableShow" title="砌体厚度配置" width="50%" center align-center destory-on-close @closed="loadthicknessTables">
                            <bidCalcThicknessTable />
                     </el-dialog>
                                         
                </el-row>
            </el-affix>
            <el-row>
                <el-col>
                    <vxe-toolbar ref="toolbarRef" custom></vxe-toolbar>
                    <vxe-table id="ruleTable" ref="dTable" :data="tableData" class="sort-table" show-overflow stripe
                        :tree-config="{expandAll: false,transform:true,expandRowKeys:treeExpandKeys}"
                        :loading="sortDataLoading" border height="840" :column-config="{ resizable: true }"
                        @cell-dblclick="cellDBClick"
                        :edit-config="{ trigger: 'dblclick', showStatus: true, mode: 'cell',beforeEditMethod:({row})=>row.temp_series=row.all_series }" 
                        :tooltip-config="{contentMethod:showAreaTooltip}"
                        :row-config="{ useKey: true, isHover: true, isCurrent: true, keyField: 'id', transform: true, height: '28' }"
                        :span-method="mergeRowColumnMethod"
                        :checkbox-config="{checkField:'connected'}"
                        @checkbox-change="checkBoxChange"
                        @toggle-tree-expand="treeExpand"
                        :row-class-name="mergeRowClass"
                        :custom-config="customConfig"
                        >
                        <vxe-column title="计算规则构件" field="bcom" width="210" align="center" :formatter="formatName" tree-node />
                        <vxe-column title="条件" class-name="conds" width="550">
                            <template #default="{ row }">
                                <bcomSortCond v-for="acond in row?.attr_conds" :cond="acond" />
                            </template>
                        </vxe-column>
                        <vxe-column title="配置" >
                            <template #default="{ row }">
                                <bcomCalcRule v-for="(cond,index) in row.calc_settings" :cond="cond" :hasNext="row?.calc_settings?.length>1 && index<(row?.calc_settings?.length-1)" />
                            </template>
                        </vxe-column>
                        <vxe-column type="checkbox" title="当前地区" width="110" class-name="checkbox-cell"></vxe-column>
                        <vxe-column title="关联地区" field="all_series" width="22%" :edit-render="{  autofocus: '.el-select' }">
                            <template #default="{row}">
                                <el-tag class="area-tag" v-for="val in row.all_series" disable-transitions >{{ allSeries.find(s=>s.id==val)?.label }}</el-tag>
                            </template>
                            <template #edit="{row}">
                                <el-select v-model="row.temp_series" placeholder="地区" multiple filterable :reserve-keyword="false" :teleported="false" collapse-tags collapse-tags-tooltip :max-collapse-tags="2" class="area-series-select" >
                                    <el-option v-for="s in allSeries" :label="s.label" :value="s.id" />
                                </el-select>
                                <el-button type="success" text icon="Select" @click.stop="saveRowRelationArea(row)" ></el-button>
                                <el-button type="danger" text icon="CloseBold" @click.stop="dTable.clearEdit()" ></el-button>
                            </template>
                        </vxe-column>
                        <vxe-column field="remarks" title="描述" width="200"></vxe-column>
                        <vxe-column title="操作" width="130">
                            <template #default="{ row }">
                                <el-button type="success" text icon="Plus" v-if="row.level!=1"
                                    @click.stop="showEditor($event.target,{ bcom: row.bcom, attr_conds: row.attr_conds,classification:row.classification,
                                    target_bcom: row.target_bcom, target_attr_conds: row.target_attr_conds, serial: row.serial,remarks: null,deduct_settings:row.deduct_settings,calc_settings:row.calc_settings })"></el-button>
                                <el-button type="primary" text icon="Edit" v-if="!row.level" @click.stop="showEditor($event.target, row)" :ref="`edit-${row.id}`"></el-button>
                                <el-button type="danger" text icon="Close" v-if="!row.level" @click.stop="DeleteData(row.id)"></el-button>
                            </template>
                        </vxe-column>
                    </vxe-table>
                </el-col>
            </el-row>
        </el-col>
    </el-row>
    <el-popover width="1310px" title="计算规则" :visible="rowEditerShow" placement="left" :virtual-ref="editRef" virtual-triggering>
        <BComCalcRuleEditor ref="bcomEditor" :saveData="saveData" :editCancel="() => rowEditerShow = false" />
    </el-popover>
    
</template>

<script>
import { ref, computed } from 'vue'

import { cloneDeep } from 'lodash-es'
import { VxeTable,VxeToolbar } from 'vxe-table';
import { GetBComAttributes } from '@/api/bComCalcAttribute'
import { QueryByClassifications, SaveAsync, DeleteAsync, ExistsAsync, QueryAreaCount, TransferData,CopyData,SaveRelationArea,SaveRelationAreas,SaveRelationAreasBySeries,DeleteRelationAreasBySeries } from '@/api/bidCalcBComRule'
import bcomSortCondEditor from '@/calcRule/BComSortCondEditor.vue'
import bcomSortCond from '@/calcRule/BComSortCond.vue'
import { ElMessage, ElMessageBox } from 'element-plus';

import BComCalcRuleEditor from '@/calcRule/BComCalcRuleEditor.vue'
import { GetBidCalcSeries } from '@/api/bidCalcSeries'
import { GetBidCalcBCom } from '@/api/bidCalcBCom'
import bcomCalcRule from '@/calcRule/BComCalcRule.vue'

import {  allAreas,newUUID  } from '@/calcRule/calc_config'
import {GetCalcThicknessConfig} from '@/api/bidCalcThicknessConfig'

import { GetBidCalcFormula } from '@/api/bidCalcFormula'
import bComCalcFormulaTable from '@/calcRule/BComCalcFormulaTable.vue'

import  bidCalcThicknessTable from '@/calcRule/BidCalcCalcThicknessTable.vue'

export default {
    components: { bcomSortCondEditor, bcomSortCond, BComCalcRuleEditor,bcomCalcRule ,bComCalcFormulaTable,bidCalcThicknessTable},
    setup() {
        const dTable = ref<VxeTable>({});
        const toolbarRef=ref<VxeToolbar>({});
        const classificationProps = { id: 'id', label: 'label', children: 'children', isLeaf: 'isLeaf', value: 'id' };
        const cprops = { multiple: true, checkStrictly: true};
        const customConfig={storage:true};
        return { dTable,toolbarRef,customConfig,classificationProps,cprops,allAreas }
    },
    data() {
        return {
            sortDataLoading: true,
            switchLoading: false,
            tableData: [],
            /**
             * 所有地区构件数量
             */
            areaCounts: [],
            /**
            * 当前选中地区
            */
            currentArea: '北京',
            /**
             * 只显示当前地区数据
             */
             showCurrentArea:false,
            /**
             * 当前系列
             */
            currentSeries: null,
            /**
             * 所有定额系列
             */
            allSeries: [],
            /**
             * 当前地区所有定额系列
             */
            areaSeries: [],
            /**
             * 操作目标系列
             */
            opAreaSeries: null,
            /**
             * 属性
             */
            bidAttributes: [],
            /**
            * 属性Map
            */
            bidAttributesMap: null,
            /**
            * 属性值Map
            */
            bidAttributesValuseMap: null,
            /**
             * 编辑弹层
             */
            rowEditerShow: false,
            /**
             * 虚拟触发编辑按钮
             */
            editRef: null,
            /**
             * 构件属性设置是否显示
             */
            bComAttributeTableShow: false,

            /**
             * 操作类型  1 复制地区  2 对比  3 复制数据
             */
            opType: 1,
            /**
             * 操作按钮
             */
            opRef: null,
            /**
             * 操作弹窗
             */
            opShow: false,
            /**
             * 操作地区
             */
            opArea: null,
            /**
             * 操作系列
             */
            opSeries: null,
            /**
             * 操作目标构件
             */
            opClassification:null,
            /**
             * 复制源构件
             */
            copySourceClassification:null,
            /**
             * 当前构件分类
             */
            currentClassification: [],
            /**
             * 构件分类
             */
            classifications: [],
            /**
             * 计算规则构件
             */
            calcBComs:[],
            /**
             * 计算规则构件Map
             */
            calcBComsMap:null,
            /**
            * 当前构件
            */
            currentBComs: [],
            /**
             * 选中行排序
             */
            currentRowSerial: null,
            /**
             * 所有构件
             */
            bcomOptions:[],
            checkedKeys:[],
            treeExpandKeys:[],
            /**
             * 砌体墙厚度配置
            */
            thicknessTables:[],
            /**
             * 计算公式弹出框
             */
            formulaTableShow:false,
            /**
             * 所有计算公式
             */
            allFormulas:[],
            /**
             * 砌体厚度配置
             */
            thicknessTableShow:false
        }
    },
    async mounted() {
        this.dTable.connect(this.toolbarRef);
        await this.loadBComAttributes();
        await this.loadSeries();
        await this.loadClassification();
        await this.loadCalcBCom();
        await this.loadAllCalcBCom();
        if (!this.currentSeries) {
            this.currentSeries = this.allSeries?.filter(s => s.area == this.currentArea)[0]?.id ?? null;
        }
        await this.loadthicknessTables();
        await this.loadSortData();
        await this.loadFormulas();
    },
    methods: {
        /**
         * 加载排序数据
         */
        async loadSortData() {
            this.sortDataLoading = true;
            let data = await QueryByClassifications(this.currentSeries,this.currentClassification);
            if(this.showCurrentArea){
                data=data.filter(d=>d.all_series?.includes(this.currentSeries));
            }
            this.initTreeNodes(data);
            this.tableData =data;
            await this.dTable.reloadData(this.tableData);
            this.sortDataLoading = false;
        },
        initTreeNodes(data){
            let tempBComs=[];
            data.forEach(d => {
                d.parentId=d.bcom.toString();
                if(tempBComs.findIndex(b=>b.id==d.bcom)==-1){
                    tempBComs.push({id:d.bcom,parentId:d.classification.toString(),level:2,bcom:d.bcom});
                }
            });
            if(tempBComs.length>0)data.unshift(...tempBComs);
            let tempClassifications=this.classifications;
            if(this.currentClassification.length>0){
                tempClassifications=this.classifications.filter(c=>this.currentClassification.map(d=>d[0]).includes(c.id));
            }
            data.unshift(...tempClassifications.map(c=>{return {id:c.id.toString(),parentId:null,level:1,name:c.name}}));
        },
        /**
         * 地区更改
         */
         async areaChange() {
            this.areaSeries = this.allSeries?.filter(s => s.area == this.currentArea);
            this.currentSeries = this.areaSeries[0]?.id ?? null;
            await this.loadSortData();
        },
        /**
         * 按地区复制数据
         */
        async copyData() {
            let result = await TransferData({ fromarea: this.currentArea, fromseries: this.currentSeries, area: this.opArea, series: this.opSeries });
            if (result > 0) {
                ElMessage({ type: 'success', message: '复制完成!' });
            }
            this.copyAreaShow = false;
            await this.loadSortData();
        },
        /**
         * 加载属性
         */
        async loadBComAttributes() {
            this.bidAttributes = await GetBComAttributes();
            this.bidAttributesMap = new Map(this.bidAttributes.map(d => [d.id, d.name]));
            this.bidAttributesValuseMap = new Map(this.bidAttributes.filter(d => d?.values?.length > 0).map(d => [d.id, d.values.split(',')]));
        },
        /**
         * 加载当前地区定额系列
         */
        async loadSeries() {
            this.allSeries = await GetBidCalcSeries();
            let tempSeries= this.allSeries.map(s=>{return {id:s.id,area:s.area,series:s.series,label:s.remarks}});
            this.allSeries=tempSeries.sort((a,b)=> this.allAreas.indexOf(a.area)- this.allAreas.indexOf(b.area));
            this.areaSeries = this.allSeries?.filter(s => s.area == this.currentArea);
        },
        /**
         * 加载对比地区定额系列
         * @param {string} val 
         */
        async loadOpAreaSeries(val) {
            this.opAreaSeries = this.allSeries?.filter(s => s.area == val);
            this.opSeries = this.opAreaSeries?.filter(s => s.id != this.currentSeries)[0]?.id ?? null;
        },
        /**
         * 保存数据
         * @param {Object} data 
         */
        async saveData(data) {
            let tempData = cloneDeep(data);
            if (tempData?.bcom) {
                if (tempData?.id) {
                    let tempRow = this.tableData.find(d => d.id == tempData.id);
                    tempRow.bcom = tempData.bcom;
                    tempRow.attr_conds = tempData.attr_conds;
                    tempRow.remarks = tempData.remarks;
                    tempRow.calc_settings = tempData.calc_settings;
                }
                else {
                    let isExists = await ExistsAsync({
                        id: '-1',
                        area: this.currentArea,
                        bcom: tempData.bcom,
                        attr_conds: tempData.attr_conds ? JSON.stringify(tempData.attr_conds) : '[]',
                        calc_settings: tempData.calc_settings ? JSON.stringify(tempData.calc_settings) : '[]',
                    });
                    if (isExists === true) {
                        ElMessage({ type: 'error', message: '当前构件关系已存在!' });
                        return false;
                    }
                    tempData.id = newUUID();
                }
                let postData = {
                    id: tempData.id,
                    bcom: tempData.bcom,
                    attr_conds: tempData.attr_conds ? JSON.stringify(tempData.attr_conds) : '[]',
                    remarks: tempData.remarks,
                    calc_settings:tempData.calc_settings ? JSON.stringify(tempData.calc_settings) : '[]'
                };
                await SaveAsync(postData);
                await this.loadSortData();
                this.rowEditerShow = false;
                await this.loadSortData();
                this.dTable.scrollToRow(this.dTable.getRowById(postData.id));
                ElMessage({ type: 'success', message: '保存成功!' });
            }
        },

        /**
         * 删除数据
         * @param {String} id 
         */
        async DeleteData(id) {
            ElMessageBox.confirm('确认删除?', 'Warning', {
                confirmButtonText: '确认',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(async () => {
                await DeleteAsync({ val: id });
                this.loadSortData();
                ElMessage({ type: 'success', message: '删除成功!' });
            })
                .catch(() => { });
        },
        /**
         * 显示行编辑弹框
         * @param {Object} btn 触发按钮
         * @param {Object} row 行数据
         */
        showEditor(btn, row) {
            this.editRef = btn;
            this.rowEditerShow = true;
            this.$refs.bcomEditor.changeData(cloneDeep(row));
        },
        /**
         * 加载构件分类
        */
        async loadClassification() {
            this.classifications = await GetBidCalcBCom(true);
            this.loadAllCalcBCom();
        },
        /**
         * 加载构件
        */
        async loadCalcBCom() {
            this.calcBComs = await GetBidCalcBCom();
            this.calcBComsMap = new Map(this.calcBComs.map(d => [d.id, d.alias]));
            this.loadAllCalcBCom();
        },
        /**
         * 加载所有构件
        */
        async loadAllCalcBCom() {
            this.bcomOptions = this.classifications.map(c=>{ return {
                    value:c.id,
                    label:c.name,
                    children: this.calcBComs.filter(d=>d.classification_id==c.id).map(d=>{return {value:d.id,label:d.alias,isLeaf:true }})
                }
            });
        },
        /**
         * 加载砌体墙厚度配置
        */
        async loadthicknessTables() {
            let temp_data=await GetCalcThicknessConfig();
            this.thicknessTables = temp_data.map(d=>{return {value:d.id,label:d.name,trans_data:d.trans_data}});
        },
        /**
        * 合并单元格
        */
        mergeRowColumnMethod({ row, $rowIndex, _columnIndex, column, visibleData }) {
            let colIndexs = [0];
            if (colIndexs.includes(_columnIndex)&&(row.level==undefined||row.level==null)) {
                let tempIndex= visibleData.findIndex(d=>d.id==row.id);
                let prevRow = visibleData[tempIndex - 1];
                if (prevRow && !prevRow.level && prevRow[column.field] === row[column.field]) {
                    return { rowspan: 0, colspan: 0 }
                } 
                else {
                    let countRowspan = 0;
                    let tempColumns=this.dTable.getColumns();
                    for (let index = tempIndex; index < visibleData.length; index++) {
                        let data = visibleData[index];
                        let isSame = false;
                        let cols= colIndexs.filter(c=>c<=_columnIndex);
                        for (let cIndex = 0; cIndex < cols.length; cIndex++) {
                            let tempField=tempColumns[cols[cIndex]].field;
                            isSame = data[tempField] === row[tempField];
                            if(!isSame)break;
                        }
                        if(isSame){
                            countRowspan++;
                        }
                        else break;
                    }
                    if (countRowspan > 1) {
                        return { rowspan: countRowspan, colspan: 1 };
                    }
                }
            }
        },

        formatClassification({row}){
            return this.classifications.find(d=>d.id==row.classification)?.name;
        },
        formatBCom(bcom){
            let temp_bcoms=bcom?.split(',');
            if(temp_bcoms?.length>0){
                let temp_name= this.calcBComs.filter(c=>temp_bcoms.includes(c.id.toString()))?.map(c=>c.alias);
                return temp_name;
            }
        },
        formatName({row}){
            return row?.name??this.formatBCom(row.bcom);
        },
         /**
         * 保存关系地区
         */
         async saveRelationArea(){
            let ids= this.tableData.filter(d=>d.connected&&!d.level).map(d=>d.id);
            await SaveRelationArea(this.currentArea,this.currentSeries,ids,this.currentClassification);
            await this.loadSortData();
            ElMessage({ type: 'success', message: '保存成功!' });
        },
        /**
         * 保存构件关联地区
         */
         async saveRowRelationArea(row){
            await SaveRelationAreas(row.id,row.temp_series);
            row.all_series=row.temp_series;
            // await this.loadSortData();
            this.dTable.clearEdit();
            ElMessage({ type: 'success', message: '保存成功!' });
        },
        /**
         * 显示构件关联地区
         */
        showAreaTooltip({row,columnIndex}){
            if(columnIndex==5&&row?.all_series?.length>5){
                return this.allSeries.filter(s=>row.all_series.includes(s.id)).map(s=>s.label).join();
            }
        },  
        /**
         * 记录展开节点
         */
         treeExpand({expanded,row}){
            if(expanded){
                this.treeExpandKeys.push(row.id);
            }
            else{
               let keyIndex= this.treeExpandKeys.indexOf(row.id);
               if(keyIndex>-1){
                this.treeExpandKeys.splice(keyIndex,1);
               }
            }
            console.log(this.treeExpandKeys);
        },
        /**
         * 合并单元格样式
         */
        mergeRowClass({row,$rowIndex}){
            let nextRow= this.dTable.getTableData().visibleData[$rowIndex+1];
            if(nextRow!=null && !row?.level && !nextRow?.level &&row.target_bcom!=nextRow.target_bcom)return 'merge-tr';
        },
        /**
         * 双击事件
         */
        cellDBClick({ row,columnIndex }){
            if(!row.level&&columnIndex!=4)this.$refs[`edit-${row.id}`].$el.click();
            else 
            {
                this.dTable.toggleTreeExpand(row);
                this.treeExpand({expanded: this.dTable.isTreeExpandByRow(row),row});
            }
        },
        /**
         * 加载计算公式
         */
         async loadFormulas() {
            this.allFormulas = await GetBidCalcFormula(1);
        },
        async checkBoxChange({checked,row}){
            let temp_rows=[];
            this.dTable.setCurrentRow(row);
            if(row.level&&row?.children?.length>0){
                if(row.level==1){
                    temp_rows=  row.children.flatMap(d=>d.children);
                }
                else{
                    temp_rows= row.children;
                }
            }
            else {
                temp_rows=[row];
            }
            temp_rows.forEach(r=>r.connected=checked);
            if (checked) {
                    await SaveRelationAreasBySeries(this.currentSeries,temp_rows.map(r=>r.id));
                    temp_rows.forEach(r=>{
                        if (r?.all_series) {
                            if(!r.all_series.includes(this.currentSeries))r.all_series.push(this.currentSeries);
                        }
                        else {
                            r.all_series = [this.currentSeries];
                        }
                    });
                    ElMessage({ type: 'success', message: `规则已关联到${this.currentArea}!` });
                }
                else {
                    ElMessageBox.confirm(`是否取消关联到${this.currentArea}`, "提示", {
                        confirmButtonText: '确认',
                        cancelButtonText: '取消',
                        type: 'error'
                    }).then(async () => {
                        await DeleteRelationAreasBySeries(this.currentSeries,temp_rows.map(r=>r.id));
                        temp_rows.forEach(r=>{
                            r.all_series=r.all_series?.filter(s=>s!=this.currentSeries);
                    });
                        ElMessage({ type: 'success', message: '取消关联成功!' });
                    }).catch(async () => {
                        temp_rows.forEach(r=>r.connected=true);
                    });
                }
                temp_rows.forEach(r=>this.dTable.reloadRow(row));
        },
    },
    provide() {
        return {
            bidAttributes: computed(() => this.bidAttributes),
            bidAttributesMap: computed(() => this.bidAttributesMap),
            bidAttributesValuseMap: computed(() => this.bidAttributesValuseMap),
            classifications: computed(() => this.classifications),
            calcBComs: computed(() => this.calcBComs),
            calcBComsMap:computed(() => this.calcBComsMap),
            bcomOptions: computed(() => this.bcomOptions),
            thicknessTables:computed(() => this.thicknessTables),
            allFormulas:computed(() => this.allFormulas)
        };
    }
}
</script>

<style scoped>
.bcom-header {
    padding: 10px;
    background-color: var(--el-color-info-light-9);
    height: 50px;
    display: block;
}

.title {
    display: flex;
    justify-content: center;
    align-items: center;
}

.link {
    display: flex;
    justify-content: right;
}

.bcom-choose {
    justify-content: left;
    align-items: center;
    background-color: white;
    padding: 0 0 10px 10px;
}

.bcom-choose .el-col {
    display: flex;
}

.bcom-choose .el-text {
    margin-right: 6px;
}

.bcom-choose .el-radio {
    margin-top: 5px;
    margin-right: 15px;
}

.bcom-choose .el-radio-group {
    max-width: 100%;
}

.bcom-card .bcom-from {
    padding: 18px 0;
}

.vxe-cell .el-button+.el-button {
    margin-left: 1px;
}

.deduct+.deduct {
    margin-left: 10px;
}


.conds {
    display: inline-flex;
    /* display: flex ; */
    align-items: center;
}

:deep(.sort-table table){
    border-collapse:collapse;
}

:deep(.sort-table .merge-tr){
    border-bottom:20px solid  #f2f3f9;
}

:deep(.sort-table .row--current .vxe-cell) {
    height: auto !important;
    max-height: none !important;
}

:deep(.row--current .attrs .vxe-cell) {
    overflow: visible;
    display: flex;
    flex-wrap: wrap;
}

:deep(.attrs .vxe-cell .el-tag) {
    white-space: break-spaces;
    margin-top: 3px;
    margin-bottom: 3px;
}

:deep(.row--current .attrs .vxe-cell .el-tag) {
    height: auto;
    min-height: 24px;
}

:deep(.deduct) {
    margin: 2px 0 2px 0;
    display:  inline-flex ;
    align-items: center;
}

:deep(.deduct+.deduct) {
    margin-left: 10px;
}

:deep(.deduct .el-text +.el-text) {
    margin-left: 5px;
}

:deep(.deduct .el-tag +.el-text) {
    margin-left: 3px;
}

:deep(.deduct .el-text +.el-tag){
    margin-left: 3px;
}

.rank-cell {
    display: flex;
    align-items: center;
}

.rank-cell .i-rank {
    width: 1em;
    height: 1em;
    margin-right: 3px;
    cursor: grab;
}

:deep(.sort-table .vxe-body--row.row--current) {
    background-color: #91d5f5;
}

:deep(.sort-table .vxe-body--row.row--hover.row--current) {
    background-color: #8bd3f5;
}

.copy-area-prop .el-select {
    margin-left: 10px;
}

.copy-area-prop .el-button {
    margin-left: 10px;
}

.copy-area-prop .el-select+.el-text {
    margin-left: 10px;
}
:deep(.bcom-choose .el-cascader){
    width: 300px;
}
:deep(.checkbox-cell .vxe-cell){
    display: flex;
    justify-content: center;
}
.area-series-select{
    width:82%;
    margin-right:5px;
}
:deep(.sort-table .el-button){
    height:24px;
    padding: 5px 9px ;
}
:deep(.sort-table .el-tag){
    height:20px;
}
:deep(.sort-table .area-tag){
    margin-bottom:2px;
}
:deep(.vxe-body--column){
    padding: 0 !important;
}
/* :deep(.vxe-body--expanded-row){
    background-color:#eee;
} */
</style>