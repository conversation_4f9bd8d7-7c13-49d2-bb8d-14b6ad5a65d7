<template>
    <div class="deduct" v-if="cond.cond_type==1">
        <el-text v-if="cond.attr_conds?.length > 0" type="info">{</el-text>
        <bcomSortCond v-for="acond in cond.attr_conds" :cond="acond" />
        <el-text v-if="cond.attr_conds?.length > 0" type="info">}</el-text>
        <el-text type="primary">{{ currentAttr }}</el-text>
        <el-text type="info">计算系数 : </el-text>
        <el-text type="success">{{ cond.factor }}</el-text>
    </div>

    <div class="deduct" v-if="cond.cond_type==2">
        <el-text v-if="cond.attr_conds?.length > 0" type="info">{</el-text>
        <bcomSortCond v-for="acond in cond.attr_conds" :cond="acond" />
        <el-text v-if="cond.attr_conds?.length > 0" type="info">}</el-text>
        <el-text type="info">属性转换 </el-text>
        <el-tag v-if="cond.attr" effect="light" disable-transitions>
            <el-text type="info">{{ `${currentAttr}  = ${bidAttributesMap.get(cond.trans_attr)} * ` }}</el-text><el-text type="success">{{ cond.factor }}</el-text>
        </el-tag>       
    </div>

    <div class="deduct" v-if="cond.cond_type==3">
        <el-text v-if="cond.attr_conds?.length > 0" type="info">{</el-text>
        <bcomSortCond v-for="acond in cond.attr_conds" :cond="acond" />
        <el-text v-if="cond.attr_conds?.length > 0" type="info">}</el-text>
        <el-text type="primary">{{ currentAttr }}</el-text>
        <el-text type="info">延伸量 : </el-text>
        <el-text type="success">{{ cond.extension }}</el-text>
    </div>

    <div class="deduct" v-if="cond.cond_type==4">
        <el-text v-if="cond.attr_conds?.length > 0" type="info">{</el-text>
        <bcomSortCond v-for="acond in cond.attr_conds" :cond="acond" />
        <el-text v-if="cond.attr_conds?.length > 0" type="info">}</el-text>
        <el-text type="primary">{{ currentAttr }}</el-text>
        <el-text type="info">砌体墙厚度计算表 : </el-text>
        <el-text type="success">{{ currentObj?.label }}</el-text>
    </div>

    <div class="deduct" v-if="cond.cond_type==5">
        <el-text v-if="cond.attr_conds?.length > 0" type="info">{</el-text>
        <bcomSortCond v-for="acond in cond.attr_conds" :cond="acond" />
        <el-text v-if="cond.attr_conds?.length > 0" type="info">}</el-text>
        <el-text type="primary">{{ currentAttr}}</el-text>
        <el-text type="info">计算公式 : </el-text>
        <el-text type="success">{{  currentObj?.name  }} : {{  currentObj?.mathml  }}</el-text>
    </div>

    <div class="deduct" v-if="cond.cond_type==6">
        <el-text v-if="cond.attr_conds?.length > 0" type="info">{</el-text>
        <bcomSortCond v-for="acond in cond.attr_conds" :cond="acond" />
        <el-text v-if="cond.attr_conds?.length > 0" type="info">}</el-text>
        <el-text type="primary">{{ currentAttr}}</el-text>
        <el-text type="info">平整场地面积 : </el-text>
        <el-text type="success">{{  getmjStr(cond.mj_type)  }}</el-text>
        <el-text v-if="cond?.extension>0" type="info">外延(扩): </el-text>
        <el-text v-if="cond?.extension>0" type="success">{{  cond.extension  }}(mm)</el-text>
    </div>

    <div class="deduct" v-if="cond.cond_type==7">
        <el-text v-if="cond.attr_conds?.length > 0" type="info">{</el-text>
        <bcomSortCond v-for="acond in cond.attr_conds" :cond="acond" />
        <el-text v-if="cond.attr_conds?.length > 0" type="info">}</el-text>
        <el-text type="primary">{{ currentAttr}}</el-text>
        <el-text type="info">保温层长度计算 : </el-text>
        <el-text type="success">{{  getbwccdStr(cond.cd_type)  }}</el-text>
    </div>

    <el-divider direction="vertical" v-if="hasNext" />
</template>
<script>
import bcomSortCond from '@/calcRule/BComSortCond.vue'
import { jzmjTypes,bwccdTypes,jckjTypes } from '@/calcRule/calc_config'

export default {
    inject:['bidAttributesMap','thicknessTables','allFormulas'],
    name: 'bcomCalcRule',
    components:{bcomSortCond},
    props: {
        cond: Object,
        hasNext:Boolean
    },
    mounted(){
        switch (this.cond.cond_type) {
            case 4:
                this.currentObj=this.thicknessTables.find(d=>d.value==this.cond.thicknessTable);
                break;
            case 5:
                this.currentObj=this.allFormulas.find(d=>d.id==this.cond.formula);
                break;
            default:
                break;
        }
    },
    data(){
        return {
            currentObj:{},
            currentAttr: this.bidAttributesMap.get(this.cond.attr)
        }
    },
    methods:{
        getmjStr(mjtype){
            return jzmjTypes.find(d=>d.value==mjtype)?.label;
        },
        getbwccdStr(cd_type){
            return bwccdTypes.find(d=>d.value==cd_type)?.label;
        }
    }
}
</script>