const channelMap=new Map();

/**
 * 发送数据
 * @param {*} channelName 通道名称 
 * @param {*} object 数据
 */
export const send=(channelName,object)=>{
    if(!channelMap.has(channelName)){
        channelMap.set(channelName,new BroadcastChannel(channelName));
    }
    channelMap.get(channelName).postMessage(object);
}

/**
 * 监听回调
 * @param {string} channelName 
 * @param {object} callback 
 */
export const listen=(channelName,callback)=>{
    if(!channelMap.has(channelName)){
        channelMap.set(channelName,new BroadcastChannel(channelName));
    }
    channelMap.get(channelName).onmessage=({data})=>callback(data);
}

/**
 * 关闭通道
 * @param {string} channelName 通道名称
 */
export const close=(channelName)=>{
    if(channelMap.has(channelName)){
        channelMap.get(channelName).close();
        channelMap.delete(channelName);
    }
}

export const channelEnum={
    sortdiff:{name:'sortdiff',comment:'排序对比'}
}