import axios from "axios"

/**
 * 获取放坡系数
 * @returns 
 */
export function GetBidCalcStepSlopeConfig(area){
    return axios.get(`/BidCalcBCom/GetCalcStepSlopeConfig?area=${area}`)
        .then((res) => {
            let tempData=res.data.data;
            tempData?.forEach(d => {
                let arr=d.start_depth?.split(',');
                d.start_depth_start=arr?.at(0);
                d.start_depth_end=arr?.at(1);
            });
            return tempData;
            // return res.data.data;
})
}

/**
 * 保存
 * @param {Object} data 
 * @returns 
 */
export function SaveData(data){
    data?.forEach(d=>{
        if(d.start_depth_start){
            d.start_depth=d.start_depth_start;
            if(d.start_depth_end){
                d.start_depth+=','+d.start_depth_end;
            }
        }
    });
    return axios.post(`/BidCalcBCom/SaveCalcStepSlopeConfig`, data)
    .then((res) => {
        return  res?.data?.succeeded;
    })
}

/**
 * 删除
 * @param {Number} id
 * @returns 
 */
export function DeleteData(id){
    return axios.post(`/BidCalcBCom/DelCalcStepSlopeConfig`, {id:id})
    .then((res) => {
        return res?.data?.succeeded;
    })
}


/**
 * 获取已配置地区
 * @returns 
 */
 export function GetAreas(){
    return axios.get(`/BidCalcBCom/GetCalcStepSlopeConfigAreas`)
        .then((res) => {
            return res.data.data;
        })
}

/**
 * 复制数据
 * @param {Number} id
 * @returns 
 */
 export function CopyCalcStepSlopeConfig(area,newArea){
    return axios.post(`/BidCalcBCom/CopyCalcStepSlopeConfig`, {area:area,newArea:newArea})
    .then((res) => {
        return res?.data?.succeeded;
    })
}