 <!--
      构件指标计算视图组件
      功能：
      1. 显示用户和项目选择界面
      2. 展示构件指标树形结构
      3. 显示指标计算结果(包括图表和表格)
      4. 提供指标异常检测功能
    -->
    <template>
        <el-container style="height: 98vh;" :loading="viewLoading">
            <el-header height="40px">
                <el-text size="large" tag="b">指标检测</el-text>
            </el-header>
            <el-container>
                <!-- 侧边栏容器（带右侧按钮） -->
                <div class="aside-wrapper">
                    <el-aside :width="isCollapse ? '5px' : '220px'" class="aside-container">
                        <el-tree ref="qTree" v-show="!isCollapse" :data="quotaTreeData" highlight-current default-expand-all :expand-on-click-node="false"  @current-change="treeChange" node-key="value">
                            <template #default="{ node, data }">
                                <el-text>
                                    {{ node.label }}
                                    <el-text v-if="data.count" :type="data?.type == 2 && data?.relationType == 2 ? 'danger' : ''">({{ data.count}})</el-text>
                                </el-text>
                            </template>
                        </el-tree>
                    </el-aside>
                    <!-- 右侧悬浮按钮 -->
                    <div class="right-collapse-btn" :class="{ 'is-collapsed': isCollapse }" @click="isCollapse=!isCollapse">
                        <Expand v-if="isCollapse" />
                        <Fold v-else />
                    </div>
                </div>
                <!-- 主内容区 -->
                <el-main>
                    <el-space wrap >
                        <el-tabs type="border-card" class="calc-tab" v-for="adata in showData" >
                            <el-tab-pane label="图形">
                                <div :ref="el => setChartRef(el, adata?.quota_id,adata?.result_index,adata?.quota_type)"></div>
                            </el-tab-pane>
                            <el-tab-pane label="数据">
                                <vxe-table v-if="adata.quota_type == 1 && adata.quota_relation_type == 2"
                                        height="480" :data="adata.all_data" show-overflow stripe
                                        border :column-config="{ resizable: true }"
                                        :row-config="{ isHover: true, isCurrent: true, transform: true, height: '28' }"
                                        :scroll-y="{ enabled: true, gt: 0 }">
                                        <vxe-column title="楼层" width="120" field="floorname" />
                                        <vxe-column title="数量" >
                                            <template #default="{ row }">
                                                <el-text v-if="row.noKeyValue" type="danger">无数据</el-text>
                                                <el-text v-else-if="row.multiples" type="danger">{{ `${row.val}(非倍数)`}}</el-text>
                                                <el-text v-else-if="row.fixedValues" type="danger">{{ `${row.val}(非固定值)`}}</el-text>
                                                <el-text v-else-if="row.hard" type="danger">{{ row.val }}</el-text>
                                                <el-text v-else-if="row.soft" type="warning">{{ row.val }}</el-text>
                                                <el-text v-else type="success">{{ row.val }}</el-text>
                                            </template>
                                        </vxe-column>
                                    </vxe-table>
                                    <el-collapse  v-else accordion >
                                        <el-collapse-item v-for="qgKey of Object.keys(adata.qgroup)" :name="qgKey">
                                            <template #title>
                                                <b>楼层: </b>
                                                <el-tag disable-transitions>{{ `${qgKey} (${adata.qgroup[qgKey]?.length ?? 0})`}}</el-tag>
                                            </template>
                                            <vxe-table height="430" :data="adata.qgroup[qgKey]" show-overflow stripe
                                                border :column-config="{ resizable: true }"  @cell-dblclick="cellDblclick"    
                                                :row-config="{ useKey: true, isHover: true, isCurrent: true, keyField: 'entobjdataid', transform: true, height: '28' }"
                                                :scroll-y="{ enabled: true, gt: 0 }">
                                                <vxe-column title="构件" width="100">
                                                    <template #default="{ row }">
                                                        <el-text >{{`${row.xgj_entname}(${row.component_name})`}}</el-text>
                                                    </template>
                                                </vxe-column>
                                                <vxe-column field="zwwz" title="轴网位置" width="100"></vxe-column>
                                                <vxe-column title="指标值" >
                                                    <template #default="{ row }">
                                                        <el-text v-if="row.noKeyValue" type="danger">无数据</el-text>
                                                        <el-text v-else-if="row.multiples" type="danger">{{ `${row.val}(非倍数)`}}</el-text>
                                                        <el-text v-else-if="row.fixedValues" type="danger">{{ `${row.val}(非固定值)`}}</el-text>
                                                        <el-text v-else-if="row.hard" type="danger">{{ row.val }}</el-text>
                                                        <el-text v-else-if="row.soft" type="warning">{{ row.val }}</el-text>
                                                        <el-text v-else type="success">{{ row.val }}</el-text>
                                                    </template>
                                                </vxe-column>
                                            </vxe-table>
                                        </el-collapse-item>
                                    </el-collapse>
                            </el-tab-pane>
                        </el-tabs>
                    </el-space>
                </el-main>
            </el-container>
        </el-container>
    </template>
    
    
    <script>
    import { ref } from 'vue'
    import { VxeTable } from 'vxe-table'
    import Highcharts, { color } from 'highcharts'
    import HighchartsMore from 'highcharts/highcharts-more'
    // import exporting from 'highcharts/modules/exporting'
    import { HoujComponentQuota, GetAllQuota } from '@/api/quota_userProjectResult'
    import { ElMessage } from 'element-plus';
    import { groupBy } from 'lodash-es'
    import { GetGraphicalComponents } from '@/api/graphicalComponent'

    
    Highcharts.setOptions({ lang: { locale: 'zh-CN' } });
    
    export default {
        /**
         * 组件setup函数
         * 初始化：
         * 1. 表格组件引用
         * 2. 树形组件属性配置
         */
        setup() {
            const treeProps = { children: "children", label: "label", value: "value" };
            return {  treeProps };
        },
        data() {
            return {
                viewLoading:false,
                isCollapse: false,
                quotaData: [],
                allComponents: [],
                quotaTreeData: [],
                allQuota: [],
                allData: [],
                showData: [],
                currentQuota: {},
                hcahrts: null
            }
        },
        async created(){
            let userprojectid=this.$route.query?.userprojectid;
            let xgj_entnames=this.$route.query?.xgj_entnames?.split(',');
            let floornames=this.$route.query?.floornames?.split(',');
            let buildingid=this.$route.query?.buildingid;
            // 验证必要参数
            if (!userprojectid || !buildingid) {
                ElMessage.error('缺少必要参数')
                return
            }
            this.viewLoading=true;
            this.allQuota = await GetAllQuota();
            this.allQuota.forEach(q=>{
                q.jsonData=JSON.parse(q.jsonData);
                q.component_name=q.jsonData.bcomsa[0].bcom;
                q.specialty=q.jsonData.bcomsa[0].bcomIds[0];
            });
            this.allComponents = await GetGraphicalComponents();
            await this.houjquota(userprojectid,buildingid,xgj_entnames,floornames);
            this.viewLoading=false;
        },
        methods: {
            chartsInit(chart) {
                // 动态生成自定义图例
                const plotBands = chart.yAxis[0].plotLinesAndBands;
                const legendX = 5; // 图例起始 X 坐标
                const legendY = 40; // 图例起始 Y 坐标

                plotBands.forEach((band, index) => {
                    if(band.options.value) return;
                    // 创建图例项的颜色块
                    chart.renderer.rect(legendX, legendY + index * 30, 18, 13)
                        .attr({
                            fill: band.options.color,
                            // stroke: 'black',
                            // 'stroke-width': 0.5
                        })
                        .add();
                        // .on('click', function () {
                        //     // 点击图例时切换 plotBand 的显示
                        //     band.svgElem.attr({ visibility: band.visible ? 'hidden' : 'visible' });
                        //     band.visible = !band.visible;
                        // });

                    // 创建图例项的文本标签
                    chart.renderer.text(band.options.description, legendX + 26, legendY + index * 30 + 12)
                        .attr({ align: 'left' })
                        .css({ fontSize: '12px' })
                        .add();
                });
            },
            getChartConfig(quota_id,result_index,qtype){
                let temp_result = null;
                let temp_quota = this.allQuota.find(d => d.id == quota_id);
                if (temp_quota) {
                    let results = temp_quota?.jsonData?.results;
                    if (results?.length > 0) temp_result = results[result_index];
                }
                var chartOptions={
                    chart:{ height:'446px'},
                    accessibility: { enabled: false },
                    credits: { enabled: false },
                    exporting: { enabled: false },
                    legend: { enabled: false},
                    title: { text: null },
                    subtitle: { text: null }
                };
                if (temp_result) {
                    let colorObj= { 标准: '#ffdc60', 最佳: '#9fe080', 异常: '#ffdc60 ' };
                    let getDesc = (val)=>{
                        if (val > temp_result.result_hard_max || val<temp_result.result_hard_min) return'异常';
                        else if (val > temp_result.result_soft_min && val < temp_result.result_soft_max) return `最佳(${temp_result.result_soft_min}~${temp_result.result_soft_max})`;
                        else if (val > temp_result.result_hard_min && val < temp_result.result_hard_max) return `标准(${temp_result.result_hard_min}~${temp_result.result_hard_max})`;
                        else return null;
                    };
                    // let temp_arr = this.allData.flatMap(a => a.quotavalues2).filter(a => a.quota_id == quota_id).flatMap(a => a.all_data);
                    //只使用第一个
                    let temp_arr = this.showData.filter(a => a.quota_id == quota_id).flatMap(a => a.all_data);
                    //标题
                    chartOptions.title.text =  temp_quota.title;
                    chartOptions.subtitle.text = `标准: ${temp_result?.result_hard_min ?? ''}~${temp_result?.result_hard_max ?? ''}    最佳: ${temp_result?.result_soft_min ?? ''}~${temp_result?.result_soft_max ?? ''}`;
                    if (temp_result?.multiples) {
                        chartOptions.subtitle.text += `  倍数: ${temp_result?.multiples}`;
                    }
                    if (temp_result?.fixedValues?.length > 0) {
                        chartOptions.subtitle.text += `<br/>固定值: ${temp_result?.fixedValues?.join(',')}`;
                    }
                    if(qtype==1){
                        // 柱状图
                        // chartOptions.chart.type='bar';
                        chartOptions.legend={ enabled: true };
                        chartOptions.xAxis={ categories: [], gridLineWidth:1,lineWidth:0 };
                        chartOptions.yAxis={ title: { text: '数量' } };
                        chartOptions.series=[];

                        let descgroup = groupBy(temp_arr.map(d => {let number_val=Number(d.val); return{ val: number_val, des: getDesc(number_val) }}),'des');
                        for(let k of Object.keys(descgroup)){
                            
                            chartOptions.series.push({ type:'bar', name:k, data: descgroup[k].map(s=>s.val), color: colorObj[k.substring(0,2)] });
                        }
                        chartOptions.xAxis.categories = temp_arr.map(d => `楼层: ${d.floorname}`);
                        chartOptions.plotOptions = { bar: { dataLabels: { enabled: true } } };
                    }
                    else if(qtype==2){
                        // 极地散点
                        chartOptions.chart.polar=true;
                        chartOptions.tooltip={
                            formatter: function () {
                                let temp_title = '指标值';//this.series.name;
                                if (this.fixedValues) {
                                    temp_title = '非固定值';
                                }
                                else if (this.multiples) {
                                    temp_title = '非倍数';
                                }
                                else if (this.noKeyValue) {
                                    temp_title = '无数据';
                                }
                                return `楼层: ${this.floorname}<br/>${temp_title}: ${this.value}`;
                            }
                        };
                        chartOptions.xAxis= {
                            gridLineWidth: 0,
                            lineWidth: 0,
                            labels: { enabled: false }
                        };
                        chartOptions.yAxis= {
                            labels: { enabled: false },
                            gridLineWidth: 0,
                            offset: -8,
                            min: 0,
                            plotBands: [
                                {
                                    from: 0,
                                    to: 270,
                                    color: "#f112125e",
                                },
                                {
                                    from: 50,
                                    to: 200,
                                    color: "#ffdc60",
                                },
                                {
                                    from: 80,
                                    to: 150,
                                    color: "#9fe080",
                                },
                            ],
                            plotLines: [{
                                // label: { text: 'max', y: 4, },
                                value: 240,
                                width: 0.5,
                                //dashStyle: "dashdot"
                            }],
                        };
                        chartOptions.series= [];
                        // 计算最大值（硬指标的1.25倍）
                        let temp_max = temp_result.result_hard_max * 1.25;
                        // 更新图表显示区域配置
                        // 添加指标值区间带（红色表示超出范围，黄色表示软指标范围，绿色表示正常范围）
                        chartOptions.yAxis.plotBands[0].to = temp_max;
                        chartOptions.yAxis.plotBands[0].description = '异常';
                        chartOptions.yAxis.plotBands[1].from = temp_result.result_hard_min;
                        chartOptions.yAxis.plotBands[1].to = temp_result.result_hard_max;
                        chartOptions.yAxis.plotBands[1].description = `标准(${temp_result.result_hard_min}~${temp_result.result_hard_max})`;
                        chartOptions.yAxis.plotBands[2].from = temp_result.result_soft_min;
                        chartOptions.yAxis.plotBands[2].to = temp_result.result_soft_max;
                        chartOptions.yAxis.plotBands[2].description = `最佳(${temp_result.result_soft_min}~${temp_result.result_soft_max})`;
                        chartOptions.yAxis.plotLines[0].value = temp_max;
                        //刻度
                        chartOptions.yAxis.tickPositions = [0, temp_result.result_hard_min, temp_result.result_soft_min, temp_result.result_soft_max, temp_result.result_hard_max, temp_max];
    
                        // 重置图表数据系列
                        chartOptions.series = [];
                        // 处理指标数据，按值分组并创建散点图系列
                        // temp_arr包含当前选中指标的所有数据点
                        if (temp_arr?.length > 0) {
                            // 按指标值对数据进行分组，相同值的数据点会被分到同一组
                            let temp_group = groupBy(temp_arr, 'val');
                            // 遍历每个不同的指标值
                            for (let nk of Object.keys(temp_group)) {
                                // 确保该值组中有数据
                                if (temp_group[nk]?.length > 0) {
                                    // 获取该组的第一个数据点作为代表
                                    let temp_group_data = temp_group[nk][0];
                                    // 判断是否为正常值：小于最大值且不存在异常标记
                                    // let isNormal = Number(nk) < temp_max && !(temp_group_data?.noKeyValue || temp_group_data?.fixedValues || temp_group_data?.multiples);
                                    let isNormal = !(temp_group_data?.noKeyValue || temp_group_data?.fixedValues || temp_group_data?.multiples);
                                    // 计算极坐标图上的角度分布（360度平均分配）
                                    let xRatio = 360 / temp_group[nk].length;
                                    chartOptions.series.push({
                                        type: "scatter",
                                        events:{
                                            click:function(e){
                                                if(!e.point.options.normal){
                                                    window.parent.postMessage({
                                                        type: 'quota',
                                                        entobjdataid: e.point.options.entobjdataid,
                                                        quota_text:e.point.options.quota_text
                                                    }, '*');
                                                }
                                            }
                                        },
                                        // name: nk,//isNormal?"正常值":'异常值',
                                        data:temp_group[nk].map((s, i) => {
                                            return {
                                                floorname: temp_group_data.floorname,
                                                x: i * xRatio,
                                                y: (Number(s.val) > temp_max || s.val == null) ? temp_max : Number(s.val),
                                                value: Number(s.val),
                                                // normal:true,
                                                entobjdataid:s.entobjdataid,
                                                quota_text:temp_group_data.quota_text,
                                                fixedValues: temp_group_data.fixedValues,
                                                multiples: temp_group_data.multiples,
                                                noKeyValue: temp_group_data.noKeyValue
                                            }
                                        }),
                                        // color: isNormal ? 'black' : 'red',
                                        // marker: { enabledThreshold: 1, radius: isNormal ? 1.3 : 2.3 },
                                        color :'black',
                                        marker: { enabledThreshold: 1, radius: 1.3 },
                                    });
                                }
                            }
                        }
                    }
                }
                return chartOptions;
            },
            setChartRef(el, quota_id,result_index,qtype) {
                if (el && quota_id && result_index>-1) {
                    let chartConfig = this.getChartConfig(quota_id,result_index,qtype);
                    Highcharts.chart(el, chartConfig,qtype==2?this.chartsInit:null);
                }
            },
            /**
             * 指标树节点选择变更处理
             * @param {Object} data - 选中的树节点数据
             */
            treeChange(data) {
                this.viewLoading=true;
                if(data.bcom){
                    this.showData=this.allData[0].quotavalues2.filter(q=>q.quota_id==data?.value);
                }
                else if(data.specialty){
                    this.showData=this.allData[0].quotavalues2.filter(q=>q.specialty==data?.value);
                }
                else{
                    this.showData=this.allData[0].quotavalues2.filter(q=>q.component_name==data.label);
                }
                this.viewLoading=false;
            },
            /**
             * 指标数据过滤器
             * @param {Object} d - 指标数据对象
             * @returns {boolean} - 是否为异常数据
             * 异常情况包括：
             * - 无数据(noKeyValue)
             * - 非固定值(fixedValues)
             * - 非倍数值(multiples)
             * - 超出硬指标范围(hard)
             * - 超出软指标范围(soft)
             * 用于筛选需要特殊处理或显示的异常指标数据
             */
            quotaDataFilter(d) {
                return d?.noKeyValue == true || d?.fixedValues == true || d?.multiples == true || d?.hard == true || d?.soft == true;
            },
            /**
             * 执行后基础信息表构件指标计算
             * @description 该方法用于处理和计算构件的指标数据
             * @async
             * @function houjquota
             * @returns {Promise<void>}
             * 
             * 主要功能：
             * 1. 清除现有图表数据
             * 2. 重置加载状态和数据结构
             * 3. 获取构件指标数据
             * 4. 处理指标树形结构
             * 5. 展示计算结果
             */
            async houjquota(userprojectid,buildingid,xgj_entnames,floornames) {
                // 重置数据结构
                this.quotaTreeData = [];
                // 获取构件指标数据
                // this.allData = await HoujComponentQuota("\\\\************\\云服务器项目同步目录\\wwb-结构_12套测试识别\\整体测试\\114号清单及图纸（新） - 副本");
                this.allData = await HoujComponentQuota(userprojectid,buildingid,xgj_entnames,floornames);
    
                if (this.allData?.length > 0) {
                    // 展平数据结构并添加建筑名称
                    let temp_list = this.allData.flatMap(qr => qr.quotavalues2.map(d => {
                        d.buildname = qr.buildname;
                        return d;
                    }));
    
                    // 关联指标定义数据
                    temp_list.forEach(qr => {
                        qr.quota = this.allQuota.find(q => q.id == qr.quota_id);
                    });
    
                    // 构建数据数组，包含指标、建筑名称、结果索引和数据
                    let distinctArrs = temp_list.map(d => {
                        return {
                            quota: d.quota,
                            buildname: d.buildname,
                            result_index: d.result_index,
                            arr: d.all_data
                        }
                    });
    
                    // 构建树形结构数据
                    let treeData = this.allQuota.map(d => {
                        let temp_bcom = d?.jsonData?.bcomsa?.at(0);
                        if(xgj_entnames && !xgj_entnames?.includes(temp_bcom?.bcom)){
                            return null;
                        }
                        return {
                            label: d.title,
                            value: d.id,
                            count: 0,
                            type: d.type,
                            relationType: d.relationType,
                            specialty: temp_bcom?.bcomIds?.at(0),
                            bcomid: temp_bcom?.bcomIds?.at(1),
                            bcom: temp_bcom?.bcom
                        }
                    }).filter(t=>t);
    
                    // 计算每个指标的统计数据
                    for (let index = 0; index < distinctArrs.length; index++) {
                        let temp_data = treeData.find(d => d?.value == distinctArrs[index].quota.id);
                        let tempArrCount = 0;
    
                        if (distinctArrs[index].arr?.length > 0) {
                            // 根据指标类型和关系类型计算统计值
                            if (distinctArrs[index].quota.type == 1 && distinctArrs[index].quota.relationType == 2) {
                                // 类型1关系2：累加值
                                tempArrCount = distinctArrs[index].arr.map(d => Number(d.val))?.reduce((p, c) => p + c);
                            } else {
                                // 其他类型：异常数据计数
                                tempArrCount = distinctArrs[index].arr?.filter(this.quotaDataFilter)?.length ?? 0;
                            }
                        }
    
                        if (temp_data) {
                            temp_data.count += tempArrCount;
                            temp_data.result_index = distinctArrs[index].result_index;
                        }
                    }
    
                    // 构建最终的树形结构
                    this.quotaTreeData = this.allComponents
                        .filter(d => treeData.find(t => t?.specialty == d.id))
                        .map(d => {
                            let temp_data = {
                                label: d.title,
                                value: d.id,
                                specialty: d.id
                            };
                            temp_data.children = d.children
                                .filter(c => treeData.find(t => t?.bcomid == c.id))
                                .map(c => {
                                    return {
                                        label: c.title,
                                        value: c.id,
                                        children: treeData.filter(td => td?.bcomid == c.id)
                                    }
                                });
                            return temp_data;
                        });
    
                    // 处理数据分组
                    this.allData.forEach(qd => {
                        qd.quotavalues2.forEach(qv => {
                            qv.qgroup = groupBy(qv.all_data, 'floorname');
                        });
                    });
                    this.showData=this.allData[0].quotavalues2;
                } else {
                    // 无异常数据时显示提示
                    ElMessage({
                        type: 'success',
                        message: '未检测到指标异常!'
                    });
                }
        },
        cellDblclick({row}){
            window.parent.postMessage({
                type: 'quota',
                entobjdataid: row.entobjdataid,
                quota_text:row.quota_text
            }, '*');
        }
    }
    }
    </script>
    
    <style scoped>
    .calc-tab{
        height: 520px;
        width: 600px;
    }
    .el-collapse{
        height: 430px;
    }
    /**
        * 构件头部样式
        * 使用浅色背景和固定高度的容器
        */
    :deep(.el-header){
        background-color: var(--el-color-info-light-8);
        height: 40px;
        display: flex;
        justify-content: center;
        align-items: center;
    }
    
    /**
         * 树形控件当前节点高亮样式
         * 使用:deep选择器处理组件内部元素
         * 使用Element Plus的浅色主题色作为背景色
         */
    :deep(.el-tree--highlight-current .el-tree-node.is-current>.el-tree-node__content) {
        background-color: var(--el-color-primary-light-7);
    }
    
    /* 侧边栏容器包装 */
    .aside-wrapper {
      position: relative;
      height: 100%;
      background-color: var(--el-color-info-light-9);
    }
    .aside-wrapper .el-tree{
        background-color: var(--el-color-info-light-9);
    }
    .aside-container {
      height: 100%;
      transition: width 0.3s ease;
      overflow: visible; /* 允许按钮溢出 */
    }
    
    /* 右侧悬浮按钮 */
    .right-collapse-btn {
      position: absolute;
      right: 0px;
      top: 50%;
      transform: translateY(-50%);
      width: 20px;
      height: 48px;
      background: #aaabac;
      border-radius: 12px 0 0 12px;
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
      cursor: pointer;
      z-index: 10;
      box-shadow: -2px 0 4px rgba(0, 0, 0, 0.1);
      transition: all 0.3s ease;
    }
    
    /* .right-collapse-btn:hover {
      background: #1f2d3d;
      right: -8px; 
    } */
    
    .right-collapse-btn.is-collapsed {
      right: -12px;
      border-radius: 0 12px 12px 0;
    }
    
    /* 菜单折叠样式 */
    .el-menu--collapse .el-sub-menu__title span {
      display: none;
    }
    
    /* 主内容区动态调整 */
    .el-main {
      transition: margin-left 0.3s ease;
    }
    .el-space{
        display:flex;
    }
    
    </style>