<template>
     <el-affix :offset="0">
                <el-row class="bcom-header">
                    <el-row class="title">
                        <el-text size="large" tag="b">吐鲁番市100万千瓦风电建设项目-风电三区</el-text>
                    </el-row>
                </el-row>
            </el-affix>
            <el-row>
                <el-divider  />
            </el-row>
    <el-row style="height:100%">
        <el-col :span="24">
            <el-row>
                <el-col :span="4">
                    <el-tree ref="qtree" :data="quotaTreeData" style="width: 100%;" highlight-current default-expand-all @current-change="treeChange" node-key="value" current-node-key="1" >
                        <template #default="{node,data}">
                            <el-text>{{ node.label }}(<el-text :type="data.errorCount>0?'danger':''">{{ data.errorCount }}</el-text>)</el-text>
                        </template>
                    </el-tree>
                </el-col>
                <el-col :span="20">
                    <el-row>
                        <el-col :span="20">
                            <!-- <el-card >
                                <template #header>
                                    <span>{{ currentQuota?.title }}</span>
                                </template>
                                <el-descriptions :title="currentQuota?.content"  :column="2" border :label-width="160">
                                    <el-descriptions-item label="标准用工">{{ `${currentQuotaReuslt?.result_hard_min??''}~${currentQuotaReuslt?.result_hard_max??''}(人·天)` }}</el-descriptions-item>
                                    <el-descriptions-item label="最佳用工">{{ `${currentQuotaReuslt?.result_soft_min??''}~${currentQuotaReuslt?.result_soft_max??''}(人·天)` }}</el-descriptions-item>
                                </el-descriptions>
                            </el-card> -->
                            <div ref="hcharts" style="height: 850px;width: 100%;"></div>
                        </el-col>
                        <el-col :span="4">
                            <el-tabs v-model="currentTab"  type="border-card" >
                                <el-tab-pane name="1" label="用工数据" >
                                    <vxe-table  ref="dTable"  height="850px" :data="showData" show-overflow stripe 
                                        border :column-config="{ resizable: true }"
                                        :row-config="{ useKey: true, isHover: true, isCurrent: true, keyField: 'entobjdataid', transform: true, height: '28' }"
                                        @current-change="currentChange" 
                                        >
                                        <vxe-column  title="风机" field="fjname" ></vxe-column>
                                        <vxe-column title="用工(人·天)" width="160" >
                                            <template #default="{ row }">
                                                <!-- <el-text v-if="row.noKeyValue" type="danger">无数据</el-text>
                                                <el-text v-else-if="row.multiples" type="danger">{{ `非倍数:${row.val}` }}</el-text>
                                                <el-text v-else-if="row.fixedValues" type="danger">{{ `非固定值:${row.val}` }}</el-text> -->
                                                <el-text v-if="row.hard" class="hard-color">{{ row.val }}</el-text>
                                                <el-text v-else-if="row.out" class="out-color">{{ row.val }}</el-text>
                                                <el-text v-else type="success">{{ row.val }}</el-text>
                                            </template>
                                        </vxe-column>
                                    </vxe-table>
                                </el-tab-pane>
                            </el-tabs>
                        </el-col>
                    </el-row>
                </el-col>
            </el-row>
        </el-col>
    </el-row>
</template>

<script>
import { ref } from 'vue'
import { VxeTable } from 'vxe-table'
import Highcharts from 'highcharts'
import HighchartsMore from 'highcharts/highcharts-more'
import exporting from 'highcharts/modules/exporting'
import { GetProjectBuilds,GetProjectResults,HoujExists,HoujComponentQuota,GetAllQuota,GetProjectUserInfo } from '@/api/quota_userProjectResult'
import { ElMessage } from 'element-plus';
import { groupBy, keys } from 'lodash-es'

Highcharts.setOptions({lang:{locale:'zh-CN'}});

export default {
    setup() {
        const dTable = ref < VxeTable > ({});
        const treeProps = { children: "children", label: "label", value: "value" };
        return { dTable, treeProps };
    },
    data() {
        return {
            allLoading:false,
            userinfos:[],
            currentUserId:null,
            projectResults: [],
            projectBuilds: [],
            quotaData: [],
            currentProjectId: null,
            currentProjectDir: null,
            showData: [],
            allComponents: [],
            projectLoading: false,
            quotaTreeData: [],
            useFloor: false,
            allQuota: [
                {
                    "id": "1",
                    "content": "起重工",
                    "jsonData": "{\"id\": \"1\", \"type\": 2, \"bcomsa\": [{\"Sort\": 1, \"bcom\": \"板\", \"bcomId\": \"5\", \"bcomIds\": [\"1684145243756302336\", \"5\"], \"isTotal\": false, \"bcomType\": \"2\", \"attr_conds\": [], \"targetAttr\": 3013, \"targetAttr_code\": \"HD\"}], \"bcomsb\": [], \"results\": [{\"Sort\": 1, \"bcom\": \"单项基本信息\", \"bcomId\": \"14\", \"bcomIds\": [\"1\", \"14\"], \"isTotal\": false, \"bcomType\": \"3\", \"multiples\": 10.0, \"attr_conds\": [], \"fixedValues\": [], \"result_hard_max\": 40, \"result_hard_min\": 12, \"result_soft_max\": 24, \"result_soft_min\": 12}], \"relationType\": 2}",
                    "relationType": 2,
                    "type": 2,
                    "discarded": false,
                    "enabled": true
                },
                {
                    "id": "2",
                    "content": "电工",
                    "jsonData": "{\"id\": \"2\", \"type\": 2, \"bcomsa\": [{\"Sort\": 1, \"bcom\": \"墙\", \"bcomId\": \"578\", \"bcomIds\": [\"1684145243756302336\", \"578\"], \"isTotal\": false, \"bcomType\": \"2\", \"attr_conds\": [], \"targetAttr\": 3013, \"isTotalNumber\": false, \"targetAttr_code\": \"HD\"}], \"bcomsb\": [], \"results\": [{\"Sort\": 1, \"bcom\": \"单项基本信息\", \"bcomId\": \"14\", \"bcomIds\": [\"1\", \"14\"], \"isTotal\": false, \"bcomType\": \"3\", \"multiples\": 3.0, \"attr_conds\": [], \"fixedValues\": [], \"isTotalNumber\": false, \"result_hard_max\": 24, \"result_hard_min\": 8, \"result_soft_max\": 16, \"result_soft_min\": 8}], \"relationType\": 2}",
                    "relationType": 2,
                    "type": 2,
                    "discarded": false,
                    "enabled": true
                },
                {
                    "id": "3",
                    "content": "钳工",
                    "jsonData": "{\"id\": \"3\", \"type\": 2, \"bcomsa\": [{\"Sort\": 1, \"bcom\": \"板\", \"bcomId\": \"5\", \"bcomIds\": [\"1684145243756302336\", \"5\"], \"isTotal\": false, \"bcomType\": \"2\", \"attr_conds\": [], \"targetAttr\": 3013, \"targetAttr_code\": \"HD\"}], \"bcomsb\": [], \"results\": [{\"Sort\": 1, \"bcom\": \"单项基本信息\", \"bcomId\": \"14\", \"bcomIds\": [\"1\", \"14\"], \"isTotal\": false, \"bcomType\": \"3\", \"multiples\": 10.0, \"attr_conds\": [], \"fixedValues\": [], \"result_hard_max\": 32, \"result_hard_min\": 8, \"result_soft_max\": 16, \"result_soft_min\": 8}], \"relationType\": 2}",
                    "relationType": 2,
                    "type": 2,
                    "discarded": false,
                    "enabled": true
                },
                {
                    "id": "4",
                    "content": "焊工",
                    "jsonData": "{\"id\": \"4\", \"type\": 2, \"bcomsa\": [{\"Sort\": 1, \"bcom\": \"墙\", \"bcomId\": \"578\", \"bcomIds\": [\"1684145243756302336\", \"578\"], \"isTotal\": false, \"bcomType\": \"2\", \"attr_conds\": [], \"targetAttr\": 3013, \"isTotalNumber\": false, \"targetAttr_code\": \"HD\"}], \"bcomsb\": [], \"results\": [{\"Sort\": 1, \"bcom\": \"单项基本信息\", \"bcomId\": \"14\", \"bcomIds\": [\"1\", \"14\"], \"isTotal\": false, \"bcomType\": \"3\", \"multiples\": 3.0, \"attr_conds\": [], \"fixedValues\": [], \"isTotalNumber\": false, \"result_hard_max\": 16, \"result_hard_min\": 4, \"result_soft_max\": 8, \"result_soft_min\": 4}], \"relationType\": 2}",
                    "relationType": 2,
                    "type": 2,
                    "discarded": false,
                    "enabled": true
                },
                {
                    "id": "5",
                    "content": "高空作业工",
                    "jsonData": "{\"id\": \"5\", \"type\": 2, \"bcomsa\": [{\"Sort\": 1, \"bcom\": \"板\", \"bcomId\": \"5\", \"bcomIds\": [\"1684145243756302336\", \"5\"], \"isTotal\": false, \"bcomType\": \"2\", \"attr_conds\": [], \"targetAttr\": 3013, \"targetAttr_code\": \"HD\"}], \"bcomsb\": [], \"results\": [{\"Sort\": 1, \"bcom\": \"单项基本信息\", \"bcomId\": \"14\", \"bcomIds\": [\"1\", \"14\"], \"isTotal\": false, \"bcomType\": \"3\", \"multiples\": 10.0, \"attr_conds\": [], \"fixedValues\": [], \"result_hard_max\": 40, \"result_hard_min\": 12, \"result_soft_max\": 24, \"result_soft_min\": 12}], \"relationType\": 2}",
                    "relationType": 2,
                    "type": 2,
                    "discarded": false,
                    "enabled": true
                },
                {
                    "id": "6",
                    "content": "安装工",
                    "jsonData": "{\"id\": \"6\", \"type\": 2, \"bcomsa\": [{\"Sort\": 1, \"bcom\": \"墙\", \"bcomId\": \"578\", \"bcomIds\": [\"1684145243756302336\", \"578\"], \"isTotal\": false, \"bcomType\": \"2\", \"attr_conds\": [], \"targetAttr\": 3013, \"isTotalNumber\": false, \"targetAttr_code\": \"HD\"}], \"bcomsb\": [], \"results\": [{\"Sort\": 1, \"bcom\": \"单项基本信息\", \"bcomId\": \"14\", \"bcomIds\": [\"1\", \"14\"], \"isTotal\": false, \"bcomType\": \"3\", \"multiples\": 3.0, \"attr_conds\": [], \"fixedValues\": [], \"isTotalNumber\": false, \"result_hard_max\": 12, \"result_hard_min\": 4, \"result_soft_max\": 8, \"result_soft_min\": 4}], \"relationType\": 2}",
                    "relationType": 2,
                    "type": 2,
                    "discarded": false,
                    "enabled": true
                },
                {
                    "id": "7",
                    "content": "普工",
                    "jsonData": "{\"id\": \"7\", \"type\": 2, \"bcomsa\": [{\"Sort\": 1, \"bcom\": \"墙\", \"bcomId\": \"578\", \"bcomIds\": [\"1684145243756302336\", \"578\"], \"isTotal\": false, \"bcomType\": \"2\", \"attr_conds\": [], \"targetAttr\": 3013, \"isTotalNumber\": false, \"targetAttr_code\": \"HD\"}], \"bcomsb\": [], \"results\": [{\"Sort\": 1, \"bcom\": \"单项基本信息\", \"bcomId\": \"14\", \"bcomIds\": [\"1\", \"14\"], \"isTotal\": false, \"bcomType\": \"3\", \"multiples\": 3.0, \"attr_conds\": [], \"fixedValues\": [], \"isTotalNumber\": false, \"result_hard_max\": 20, \"result_hard_min\": 6, \"result_soft_max\": 12, \"result_soft_min\": 6}], \"relationType\": 2}",
                    "relationType": 2,
                    "type": 2,
                    "discarded": false,
                    "enabled": true
                },
            ],
            allData: [],
            tabsData:[],
            currentTab:"1",
            currentCollapse:null,
            currentQuota:{},
            currentQuotaReuslt:{},
            hcahrts:null,
            chartOptions: {
                accessibility: { enabled: false },
                credits: { enabled: false },
                chart: {
                    polar: true,
                },
                legend: {
                    align: "left",
                    verticalAlign: "middle",
                    layout: "vertical",
                },
                tooltip: {
                    flollowPointer:true,
                    formatter: function () {
                        return `${this.fjname}: ${this.value} (人·天)`;
                    }
                },
                title: { text: null,align:'center'},
                subtitle:{text:null,align:'center'},
                xAxis: {
                    gridLineWidth: 0,
                    lineWidth: 0,
                    labels: { enabled: false }
                },
                yAxis: {
                    gridLineWidth: 0,
                    offset:-8,
                    min: 0,
                    plotBands: [
                        {
                            from: 0,
                            to: 270,
                            color: "#f112125e",
                        },
                        {
                            from: 50,
                            to: 200,
                            color: "#ffdc60",
                        },
                        {
                            from: 80,
                            to: 150,
                            color: "#9fe080",
                        },
                    ],
                    plotLines: [{
                        label:{text:'',y:4, },
                        value: 240,
                        width: 1,
                        dashStyle: "dashdot"
                        }],
                },
                series: [
                     {
                        type: "scatter",
                        name: "正常用工(人·天)",
                        data: [],
                        color: 'black',
                        marker: { radius: 3 }
                    },
                    {
                        type: "scatter",
                        name: "异常用工(人·天)",
                        data: [],
                        color: "red",
                        marker: { radius: 4 },
                        clip: false
                    },
                ]
            }
        };
    },
    async mounted() {
        this.quotaTreeData=[{label:'单风机用工指标',value:'2-2',children:[
        {label:'起重工',value:'1',errorCount:1},
        {label:'电工',value:'2',errorCount:0},
        {label:'钳工',value:'3',errorCount:3},
        {label:'焊工',value:'4',errorCount:5},
        {label:'高空作业工',value:'5',errorCount:0},
        {label:'安装工',value:'6',errorCount:4},
        {label:'普工',value:'7',errorCount:4},
        ]}];
        this.allData=[
            {
                quota_id: "1",
                result_index: 0,
                all_data: [
                    { fjname: "风机#1",val: 20,hard:false,soft:true },
                    { fjname: "风机#2",val: 20,hard:false,soft:true },
                    { fjname: "风机#3",val: 19,hard:false,soft:true },
                    { fjname: "风机#4",val: 20,hard:false,soft:true },
                    { fjname: "风机#5",val: 20,hard:false,soft:true },
                    { fjname: "风机#6",val: 10,hard:false,soft:false,out:true},
                    { fjname: "风机#7",val: 22.5,hard:false,soft:true },
                    { fjname: "风机#8",val: 22,hard:false,soft:true },
                    { fjname: "风机#9",val: 22,hard:false,soft:true },
                    { fjname: "风机#10",val: 18,hard:false,soft:true },
                    { fjname: "风机#11",val: 18.5,hard:false,soft:true },
                    { fjname: "风机#12",val: 18,hard:false,soft:true },
                    { fjname: "风机#13",val: 19.5,hard:false,soft:true },
                    { fjname: "风机#14",val: 25,hard:true,soft:false },
                    { fjname: "风机#15",val: 25,hard:true,soft:false },
                    { fjname: "风机#16",val: 25,hard:true,soft:false },
                    { fjname: "风机#17",val: 17.5,hard:false,soft:true },
                    { fjname: "风机#18",val: 28,hard:true,soft:false },
                    { fjname: "风机#19",val: 28,hard:true,soft:false },
                    { fjname: "风机#20",val: 28,hard:true,soft:false },
                ]
            },
            {
                quota_id: "2",
                result_index: 0,
                all_data: [
                    { fjname: "风机#1",val: 13,hard:false,soft:true },
                    { fjname: "风机#2",val: 13,hard:false,soft:true },
                    { fjname: "风机#3",val: 15,hard:false,soft:true },
                    { fjname: "风机#4",val: 13,hard:false,soft:true },
                    { fjname: "风机#5",val: 20,hard:true,soft:false },
                    { fjname: "风机#6",val: 13.5,hard:false,soft:true },
                    { fjname: "风机#7",val: 15,hard:false,soft:true },
                    { fjname: "风机#8",val: 14.5,hard:false,soft:true },
                    { fjname: "风机#9",val: 13,hard:false,soft:true },
                    { fjname: "风机#10",val: 22,hard:true,soft:false },
                    { fjname: "风机#11",val: 18,hard:true,soft:false },
                    { fjname: "风机#12",val: 19,hard:true,soft:false },
                    { fjname: "风机#13",val: 22.5,hard:true,soft:false },
                    { fjname: "风机#14",val: 15,hard:false,soft:true  },
                    { fjname: "风机#15",val: 15,hard:false,soft:true  },
                    { fjname: "风机#16",val: 15,hard:false,soft:true  },
                    { fjname: "风机#17",val: 19,hard:true,soft:false },
                    { fjname: "风机#18",val: 14.5,hard:false,soft:true },
                    { fjname: "风机#19",val: 15,hard:false,soft:true },
                    { fjname: "风机#20",val: 17,hard:true,soft:false },
                ]
            },
            {
                quota_id: "3",
                result_index: 0,
                all_data: [
                    { fjname: "风机#1",val: 15,hard:false,soft:true },
                    { fjname: "风机#2",val: 30,hard:true,soft:false },
                    { fjname: "风机#3",val: 28.5,hard:true,soft:false },
                    { fjname: "风机#4",val: 33,hard:false,soft:false,out:true },
                    { fjname: "风机#5",val: 30,hard:true,soft:false },
                    { fjname: "风机#6",val: 29,hard:true,soft:false },
                    { fjname: "风机#7",val: 22.5,hard:true,soft:false },
                    { fjname: "风机#8",val: 19.5,hard:true,soft:false },
                    { fjname: "风机#9",val: 34,hard:false,soft:false,out:true },
                    { fjname: "风机#10",val: 28,hard:true,soft:false },
                    { fjname: "风机#11",val: 26.5,hard:true,soft:false },
                    { fjname: "风机#12",val: 22,hard:true,soft:false },
                    { fjname: "风机#13",val: 14.5,hard:false,soft:true },
                    { fjname: "风机#14",val: 29,hard:true,soft:false },
                    { fjname: "风机#15",val: 7.5,hard:false,soft:false ,out:true },
                    { fjname: "风机#16",val: 22,hard:false,soft:true },
                    { fjname: "风机#17",val: 22,hard:false,soft:true },
                    { fjname: "风机#18",val: 28,hard:true,soft:false },
                    { fjname: "风机#19",val: 28,hard:true,soft:false },
                    { fjname: "风机#20",val: 28,hard:true,soft:false },
                ]
            },
            {
                quota_id: "4",
                result_index: 0,
                all_data: [
                    { fjname: "风机#1",val: 7,hard:false,soft:true },
                    { fjname: "风机#2",val: 7,hard:true,soft:false },
                    { fjname: "风机#3",val: 9,hard:false,soft:true },
                    { fjname: "风机#4",val: 7,hard:false,soft:true },
                    { fjname: "风机#5",val: 8,hard:false,soft:true },
                    { fjname: "风机#6",val: 9,hard:true,soft:false },
                    { fjname: "风机#7",val: 11,hard:true,soft:false },
                    { fjname: "风机#8",val: 9,hard:true,soft:false },
                    { fjname: "风机#9",val: 19,hard:false,soft:false,out:true },
                    { fjname: "风机#10",val: 18,hard:false,soft:false,out:true  },
                    { fjname: "风机#11",val: 18,hard:false,soft:false,out:true   },
                    { fjname: "风机#12",val: 5,hard:false,soft:true },
                    { fjname: "风机#13",val: 12,hard:true,soft:false },
                    { fjname: "风机#14",val: 13,hard:true,soft:false },
                    { fjname: "风机#15",val: 19,hard:false,soft:false,out:true   },
                    { fjname: "风机#16",val: 19,hard:false,soft:false,out:true   },
                    { fjname: "风机#17",val: 9,hard:true,soft:false },
                    { fjname: "风机#18",val: 7,hard:false,soft:true },
                    { fjname: "风机#19",val: 5,hard:false,soft:true },
                    { fjname: "风机#20",val: 5,hard:false,soft:true },
                ]
            },
            {
                quota_id: "5",
                result_index: 0,
                all_data: [
                    { fjname: "风机#1",val: 18,hard:false,soft:true },
                    { fjname: "风机#2",val: 18,hard:false,soft:true },
                    { fjname: "风机#3",val: 18,hard:false,soft:true },
                    { fjname: "风机#4",val: 20,hard:false,soft:true },
                    { fjname: "风机#5",val: 20,hard:false,soft:true },
                    { fjname: "风机#6",val: 23,hard:false,soft:true },
                    { fjname: "风机#7",val: 19.5,hard:false,soft:true },
                    { fjname: "风机#8",val: 19,hard:false,soft:true },
                    { fjname: "风机#9",val: 20,hard:false,soft:true },
                    { fjname: "风机#10",val: 17,hard:false,soft:true },
                    { fjname: "风机#11",val: 17.5,hard:false,soft:true },
                    { fjname: "风机#12",val: 17,hard:false,soft:true },
                    { fjname: "风机#13",val: 16.5,hard:false,soft:true },
                    { fjname: "风机#14",val: 23,hard:true,soft:false },
                    { fjname: "风机#15",val: 23,hard:true,soft:false },
                    { fjname: "风机#16",val: 23,hard:true,soft:false },
                    { fjname: "风机#17",val: 18,hard:false,soft:true },
                    { fjname: "风机#18",val: 28,hard:true,soft:false },
                    { fjname: "风机#19",val: 28,hard:true,soft:false },
                    { fjname: "风机#20",val: 28,hard:true,soft:false },
                ]
            },
            {
                quota_id: "6",
                result_index: 0,
                all_data: [
                    { fjname: "风机#1",val: 5,hard:false,soft:true },
                    { fjname: "风机#2",val: 5,hard:true,soft:false },
                    { fjname: "风机#3",val: 10,hard:false,soft:true },
                    { fjname: "风机#4",val: 6,hard:false,soft:true },
                    { fjname: "风机#5",val: 7,hard:false,soft:true  },
                    { fjname: "风机#6",val: 6.5,hard:false,soft:true  },
                    { fjname: "风机#7",val: 6.5,hard:false,soft:true  },
                    { fjname: "风机#8",val: 14,hard:false,soft:false,out:true },
                    { fjname: "风机#9",val: 15,hard:false,soft:false,out:true },
                    { fjname: "风机#10",val: 11,hard:false,soft:true  },
                    { fjname: "风机#11",val: 11,hard:false,soft:true   },
                    { fjname: "风机#12",val: 5,hard:false,soft:true },
                    { fjname: "风机#13",val: 7,hard:false,soft:true  },
                    { fjname: "风机#14",val: 3.5,hard:false,soft:false,out:true},
                    { fjname: "风机#15",val: 7,hard:false,soft:true   },
                    { fjname: "风机#16",val: 7,hard:false,soft:true  },
                    { fjname: "风机#17",val: 3.5,hard:false,soft:false,out:true },
                    { fjname: "风机#18",val: 7,hard:false,soft:true },
                    { fjname: "风机#19",val: 5,hard:false,soft:true },
                    { fjname: "风机#20",val: 5,hard:false,soft:true },
                ]
            },
            {
                quota_id: "7",
                result_index: 0,
                all_data: [
                    { fjname: "风机#1",val: 13,hard:true,soft:false },
                    { fjname: "风机#2",val: 8,hard:true,soft:false },
                    { fjname: "风机#3",val: 10,hard:false,soft:true },
                    { fjname: "风机#4",val: 9,hard:false,soft:true },
                    { fjname: "风机#5",val: 14,hard:true,soft:false },
                    { fjname: "风机#6",val: 19,hard:true,soft:false },
                    { fjname: "风机#7",val: 18,hard:true,soft:false },
                    { fjname: "风机#8",val: 21,hard:false,soft:false,out:true },
                    { fjname: "风机#9",val: 22,hard:false,soft:false,out:true },
                    { fjname: "风机#10",val: 5.5,hard:false,soft:false,out:true  },
                    { fjname: "风机#11",val: 5,hard:false,soft:false,out:true   },
                    { fjname: "风机#12",val: 8,hard:false,soft:true },
                    { fjname: "风机#13",val: 12,hard:false,soft:true },
                    { fjname: "风机#14",val: 13,hard:true,soft:false},
                    { fjname: "风机#15",val: 11,hard:true,soft:true },
                    { fjname: "风机#16",val: 10.5,hard:false,soft:true  },
                    { fjname: "风机#17",val: 14,hard:true,soft:false},
                    { fjname: "风机#18",val: 12,hard:false,soft:true },
                    { fjname: "风机#19",val: 11,hard:false,soft:true },
                    { fjname: "风机#20",val: 18,hard:true,soft:false },
                ]
            }
        ];
        this.showData= this.allData.find(d=>d.quota_id=="1").all_data;
        this.currentQuota=this.allQuota.find(d=>d.id=="1");
        this.currentQuotaReuslt=JSON.parse(this.currentQuota.jsonData)?.results?.at(0);
        this.treeChange({label:'起重工',value:'1'});
     },
    methods: {
        treeChange(data){
            this.clearHighCharts();
            this.currentQuota=this.allQuota.find(d=>d.id==data.value);
            let temp_result=null;
            if( this.currentQuota){
                temp_result=JSON.parse(this.currentQuota.jsonData)?.results?.at(0);
            }
            this.currentQuotaReuslt=temp_result;
            if(temp_result){
                this.chartOptions.title.text=this.currentQuota?.content;
                this.chartOptions.subtitle.text= `标准用工: ${this.currentQuotaReuslt?.result_hard_min??''}~${this.currentQuotaReuslt?.result_hard_max??''}(人·天)</br>最佳用工: ${this.currentQuotaReuslt?.result_soft_min??''}~${this.currentQuotaReuslt?.result_soft_max??''}(人·天)`;
                let temp_arr=this.allData.filter(a=>a.quota_id==data.value).flatMap(a=>a.all_data);
                this.showData=temp_arr;
                //最大值
                let temp_max=temp_result.result_hard_max*1.25;
                //标示带
                this.chartOptions.yAxis.plotBands[0].to=temp_max;
                this.chartOptions.yAxis.plotBands[1].from=temp_result.result_hard_min;
                this.chartOptions.yAxis.plotBands[1].to=temp_result.result_hard_max;
                this.chartOptions.yAxis.plotBands[2].from=temp_result.result_soft_min;
                this.chartOptions.yAxis.plotBands[2].to=temp_result.result_soft_max;
                this.chartOptions.yAxis.plotLines[0].value=temp_max;
                this.chartOptions.yAxis.tickPositions=[0,temp_result.result_hard_min,temp_result.result_soft_min,temp_result.result_soft_max,temp_result.result_hard_max,temp_max];
                //正常数据散点
                let normalData=temp_arr.filter(d=>d?.hard==true||d?.soft==true);
                this.chartOptions.series[0].data=normalData?.map(d=>{return {fjname:d.fjname,y:Number(d.val), value:Number(d.val)}});
                //异常数据散点
                let errorData=temp_arr.filter(d=>d?.out==true);
                this.chartOptions.series[1].data=errorData?.map(d=>{return {fjname:d.fjname,y:Number(d.val), value:Number(d.val)}});
                //初始化hcharts
                this.hcahrts=Highcharts.chart(this.$refs.hcharts, this.chartOptions);
            }
           
        },
        /**
         * 过滤
         */
        quotaDataFilter(d){
            return d?.noKeyValue==true || d?.fixedValues==true || d?.multiples==true||d?.hard==true||d?.soft==true;
        },
        /**
         * 执行构件指标(后基础信息表)
         */
        async houjquota(){
            this.clearHighCharts();
            this.quotaTreeData=[];
            this.tabsData=[];
            this.allData= await HoujComponentQuota(this.currentProjectDir);

            if( this.allData?.length>0){
                let temp_list= this.allData.flatMap(qr=>qr.quotavalues2.map(d=>{d.buildname=qr.buildname;return d;}));
                temp_list.forEach(qr=>{
                    qr.quota=this.allQuota.find(q=>q.id==qr.quota_id);
                });
                let distinctArrs= temp_list.map(d=>{return {quota:d.quota,buildname:d.buildname,result_index:d.result_index,arr:d.all_data }});

                this.allData.forEach(qd=>{
                    qd.arr=qd.quotavalues2.flatMap(d=>d.all_data);
                    qd.qgroup=groupBy(qd.arr,'floorname');
                });
                this.tabsData=this.allData.map(d=>{return {buildname:d.buildname,qgroup:d.qgroup}});
                let tempTabData=this.tabsData?.at(0);
                if(tempTabData){
                    this.currentTab=tempTabData.buildname;
                    this.currentCollapse= [Object.keys(tempTabData.qgroup)?.at(0)];
                }
          }
          else{
            await ElMessage({ type: 'success', message: '未检测到指标异常!' });
          }
        },
        /**
         * 清理hcharts
         */
        clearHighCharts(){
            if(this.hcahrts){
                if( this?.hcahrts?.destory)this?.hcahrts?.destory();
                this.$refs.hcharts.innerText='';
            }
        },
        currentChange({ row }){
            if(this.hcahrts){
                let temp_point=this.hcahrts.series[(row?.out==true)?1:0].points.find(p=>p.fjname==row.fjname);
                // temp_point.category=temp_point.fjname;
                this.hcahrts.tooltip.refresh([temp_point]);
            }
        }
    },
}
</script>

<style scoped>
.bcom-header {
    padding: 10px;
    background-color: var(--el-color-info-light-9);
    height: 40px;
    display: block;
}

.title {
    display: flex;
    justify-content: center;
    align-items: center;
}

.error-col{
    color:var(--el-text-color);
}
:deep(.el-tree--highlight-current .el-tree-node.is-current>.el-tree-node__content){
    background-color: var(--el-color-primary-light-7);
}
.hard-color{
    color: #e8b600fe
}
.out-color{
    color: red
}
</style>