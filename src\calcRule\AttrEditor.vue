<template>
    <el-tag  disable-transitions type="info" closable class="bg-purple2" @close="delCond" >
        <template #default>
            <el-text size="small" style="margin-right: 3px;" >{{ getAttrName(this.attrName) }}</el-text>
            
            <!-- 范围 -->
            <el-input v-if="attrType=='range'" class="ew" size="small" placeholder="起始" v-model="conds[attrName][0]"></el-input>
            <el-text v-if="attrType=='range'"> ~ </el-text>
            <el-input v-if="attrType=='range'" class="ew" size="small" placeholder="终止" v-model="conds[attrName][1]"></el-input>

            <!-- 枚举 -->
            <el-select v-if="attrType=='enum'" class="ew-mselect" size="small" :placeholder="getAttrName(this.attrName)" v-model="conds[attrName]"  clearable filterable multiple collapse-tags >
                <!-- 材质 -->
                <el-option v-if="attrName=='cz'" v-for="item in wkCondCZs" :label="item" :value="item" />
                <!-- 非材质 -->
                <el-option v-if="attrName=='fcz'" v-for="item in wkCondCZs" :label="item" :value="item" />
                <!-- 面层 -->
                <el-option v-if="attrName=='mc'" v-for="item in wkCondMCs" :label="item" :value="item" />
                <!-- 基础类型 -->
                <el-option v-if="attrName=='jclx'" v-for="item in wkCondJCLXs" :label="item" :value="item" />
                <!-- 挡土板面数 -->
                <el-option v-if="attrName=='dtbms'" v-for="item in wkCondDTBMS" :label="item" :value="item" />
            </el-select>

            <!-- 禁用滑块 -->
            <el-switch v-if="attrType=='u-switch'" v-model="conds[attrName]" disabled></el-switch>

        </template>
    </el-tag>
</template>
<script>

import {wkCondOptions,wkCondCZs ,wkCondMCs,wkCondJCLXs,wkCondDTBMS}  from '@/calcRule/calc_config'
export default {
    name: 'attrEditor',
    props: {
        /**
         * 删除func
         */
        delCond:Function,
        /**
         * 属性名
         */
        attrName:String,
        /**
         * 属性
         */
         conds: Object,
    },
    mounted(){
        this.attrType= wkCondOptions.find(p=>p.value==this.attrName)?.type; 
    },
    setup(){
        return {wkCondOptions,wkCondCZs ,wkCondMCs,wkCondJCLXs,wkCondDTBMS}
    },
    data(){
        return {
            /**
             * * 属性类型
             * */
            attrType: null,
        }
    },
    methods:{
        getAttrName:(name)=>wkCondOptions.find(w=>w.value==name)?.label??name
    },
}
</script>

<style scoped>

.ew {
    width: 142px !important;
}

.ew-mselect {
    width: 172px !important;
}
.bg-purple2 {
    padding: 0 3px 0 3px;
    display: flex;
    align-items: center;
    align-self: center;
    margin: 3px;
    min-height: 32px;
}

:deep(.el-tag__content){
    display: flex;
    align-items: center;
}

:deep(.el-tag__content .el-text+.el-select){
    margin-left: 8px;
}

</style>
