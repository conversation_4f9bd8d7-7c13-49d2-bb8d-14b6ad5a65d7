<template>
     <div class="deduct" >
        <el-text type="primary">{{ formatName(row.bcom) }} </el-text>
        <el-text v-if="row.attr_conds?.length>0" type="info">{</el-text>
        <bcomSortCond v-for="(acond,index) in row.attr_conds" :cond="acond" :hasNext="row?.attr_conds?.length>1 && index<(row?.attr_conds?.length-1)"  />
        <el-text v-if="row.attr_conds?.length>0" type="info">}</el-text>
        <el-text v-if="setting.operation==1||setting.operation==3||setting.operation==4||setting.operation==5" type="danger">扣除</el-text>
        <el-text v-else-if="setting.operation==2||setting.operation==6" type="warning">不扣除</el-text>
        <!-- <el-text v-else-if="setting.operation" type="danger">{{ deductOptions.find(d=>d.value==setting.operation)?.label }}</el-text> -->
        <el-text type="info" >  与</el-text>
                
        <el-text type="primary">{{ formatBCom(row?.target_bcom) }} </el-text>
        <el-text v-if="row.target_attr_conds?.length>0" type="info">{</el-text>
        <bcomSortCond v-for="(tacond,index) in row.target_attr_conds" :cond="tacond" :hasNext="row?.target_attr_conds?.length>1 && index<row?.target_attr_conds?.length-1"  />
        <el-text v-if="row.target_attr_conds?.length>0" type="info">}</el-text>
                        
        <el-text v-if="setting.operation==3" type="info" >平行相交部分, <el-text type="warning">不扣除 </el-text>非平行相交部分</el-text>
        <el-text v-else-if="setting.operation==4" type="info" >非平行相交部分, <el-text type="warning">不扣除 </el-text>平行相交部分</el-text>
        <el-text v-else-if="setting.operation==5||setting.operation==6" type="info" >凸出墙面部分</el-text>
        <el-text v-else type="info">相交部分</el-text>
        <el-text v-if="setting?.deduct_conds?.length>0" type="info">{</el-text>
        <bcomSortCond v-for="(dcond,index) in setting?.deduct_conds" :cond="dcond" :hasNext="setting?.deduct_conds?.length>1 && index<setting?.deduct_conds?.length-1" />
        <el-text v-if="setting?.deduct_conds?.length>0" type="info">}</el-text>
        <el-text type="primary">{{bidAttributesMap.get(setting.attr) }}</el-text>
     </div>

    <el-divider direction="vertical" v-if="hasNext" />
</template>
<script>
import bcomSortCond from '@/calcRule/BComSortCond.vue'
import {deductOptions} from '@/calcRule/calc_config'

export default {
    inject:['calcBComs','bidAttributesMap'],
    name: 'bcomDecuctCond',
    components:{bcomSortCond},
    props: {
        row:Object,
        setting:Object,
        hasNext:Boolean
    },
    setup(){
        return{deductOptions}
    },
    data(){
        return {
        }
    },
    methods:{
        formatName(bcom){
           return  bcom?.split(',')?.map(c=>this.formatBCom(c)).join(',');
        },
        formatBCom(bcom){
            if(bcom)return this.calcBComs.find(c=>c.id==Number(bcom))?.alias;
        },
    }
}
</script>