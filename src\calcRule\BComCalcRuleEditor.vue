<template>
        <el-form label-width="100px" >
            <el-form-item label="计算规则构件" class="cond-tr">
                <el-cascader class="bcoms-cascader" v-model="currentBComs" :props="cprops" placeholder="构件选择" :options="bcomOptions"
                filterable clearable collapse-tags collapse-tags-tooltip  @change="bcomChange" >
                <template #default="{node,data}">
                        <el-text v-if="data.conds?.length>0" type="success" >{{ data.label }}</el-text>
                        <span v-else>{{ data.label }}</span>
                    </template>
                </el-cascader>
                <el-text class="deductText">条件</el-text>
                <bcomSortCondEditor v-for="(acond, aindex)  in currentRow.attr_conds" :cond="acond" :delCond="()=>delCond(currentRow.attr_conds,aindex)"  />
                <el-popover placement="right" trigger="click" >
                    <template #reference>
                        <el-button size="small" icon="plus" circle type="primary" class="btn-plus"></el-button>
                    </template>
                    <el-select size="small" @change="(op)=>addCond(currentRow,'attr_conds',op)" placeholder="选择条件" >
                        <el-option v-for="item in condOptions" :key="item.value" :label="item.label" :value="item.value" />
                    </el-select>
                </el-popover>
            </el-form-item>
            <el-form-item label="计算规则" >
                <el-tabs v-model="editableTab" type="border-card" addable @tab-add="handleTabsAdd" class="bcom-tabs">
                    <el-tab-pane v-for="(ded,index) in currentRow.calc_settings" :key="ded" :name="index" >
                        <template #label>
                            <Rank class="i-rank" /><el-text>{{ bidAttributesMap.get(ded.attr) }}</el-text> <el-icon class="is-icon-close" ><Close @click.stop="showTabDelConfirm($event.target, ()=>delTabs(index))" /></el-icon>
                        </template>
                        <el-form label-width="220px" >
                            <el-row>
                                <el-col :span="8">
                                    <el-form-item label="计算属性">
                                        <el-select class="ew" placeholder="属性" v-model="ded.attr" filterable remote remote-show-suffix :remote-method="attributesLoad" :loading="atLoading" >
                                            <el-option v-for="attr in localAttributes" :label="attr.name" :value="attr.id" />
                                        </el-select>
                                    </el-form-item>
                                </el-col>
                                <el-col :span="8">
                                    <el-form-item label="计算规则类型">
                                        <el-select v-model="ded.cond_type" placeholder="计算规则类型" @change="(val)=>condTypeChange(index,val)">
                                            <el-option v-for="item in ruleTypes" :key="item.value" :label="item.label" :value="item.value" />
                                        </el-select>
                                    </el-form-item>
                                </el-col>
                                <el-col :span="8" v-if="ded.cond_type==4">
                                    <el-form-item label="砌体墙厚度配置">
                                        <el-select v-model="ded.thicknessTable" placeholder="砌体厚度配置" @change="thicknessTableChange">
                                            <el-option v-for="item in thicknessTables" :key="item.value" :label="item.label" :value="item.value" />
                                        </el-select>
                                    </el-form-item>
                                </el-col>
                                <el-col :span="8" v-if="ded.cond_type==5">
                                    <el-form-item label="计算公式">
                                    <el-select class="ew"  placeholder="公式" v-model="ded.formula" @change="(val)=>formulaChange(val,ded)" >
                                        <el-option v-for="f in allFormulas" :label="f.name" :value="f.id" />
                                    </el-select>
                                </el-form-item>
                                </el-col>
                            </el-row>
                            <el-divider style="margin: 2px 0px 5px 0;" />
                            <el-row v-if="ded.cond_type==1">
                                <el-form-item label="计算系数" >
                                    <el-input-number placeholder="计算系数" v-model="ded.factor" style="width: 300px;" />
                                </el-form-item>
                            </el-row>
                            <el-row v-if="ded.cond_type==2">
                                <el-col :span="8">
                                    <el-form-item label="转换原属性">
                                        <el-select class="ew"  placeholder="属性" v-model="ded.trans_attr" filterable remote remote-show-suffix :remote-method="attributesLoad" :loading="atLoading" >
                                            <el-option v-for="attr in localAttributes" :label="attr.name" :value="attr.id" />
                                        </el-select>
                                    </el-form-item>
                                </el-col>
                                <el-col :span="8">
                                    <el-form-item label="转换系数">
                                        <el-input-number placeholder="转换系数" v-model="ded.factor" style="width: 200px;"/>
                                    </el-form-item>
                                </el-col>
                            </el-row>
                            <el-row v-if="ded.cond_type==3">
                                <el-col :span="8">
                                    <el-form-item label="延伸属性">
                                        <el-select class="ew"  placeholder="属性" v-model="ded.extend_attr" filterable remote remote-show-suffix :remote-method="attributesLoad" :loading="atLoading" >
                                            <el-option v-for="attr in localAttributes" :label="attr.name" :value="attr.id" />
                                        </el-select>
                                    </el-form-item>
                                </el-col>
                                <el-col :span="8">
                                    <el-form-item label="延伸量">
                                        <el-input-number placeholder="延伸量" v-model="ded.entended" style="width: 300px;"/>
                                    </el-form-item>
                                </el-col>
                            </el-row>
                            <el-row v-if="ded.cond_type==4">
                                <el-col :span="14">
                                    <vxe-table border size="mini" :data="tableData" :row-config="{height:28}" show-overflow >
                                        <vxe-column field="thickness" title="设计厚度" ></vxe-column>
                                        <vxe-column field="calc_thickness" title="计算厚度" ></vxe-column>
                                    </vxe-table>
                                </el-col>
                            </el-row>
                            <el-row  v-if="ded.cond_type == 5">
                                <el-form-item label="公式内容">
                                    <el-text type="primary">{{ allFormulas.find(d=>d.id==ded.formula)?.mathml }}</el-text>
                                </el-form-item>
                            </el-row>
                            <div v-if="ded.cond_type == 5" v-for="fp in ded.fparams" class="cond-tr" >
                                <el-row>
                                    <el-col :span="6">
                                        <el-form-item label="参数名称">
                                            <el-text type="primary">{{ fp.name }}</el-text>
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="8">
                                        <el-form-item label="取值方式">
                                            <el-select class="ew" size="small" placeholder="取值方式" v-model="fp.attrValued" >
                                                <el-option v-for="vt in attrValuedTypes" :label="vt.label" :value="vt.value" />
                                            </el-select>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <el-row>
                                    <el-form-item label="取值">
                                        <el-text v-if="!fp.attrValued || fp.attrValued == 0" type="primary">内置属性</el-text>
                                        <el-input-number v-if="fp.attrValued == 1" size="small" placeholder="数值" v-model="fp.val" style="width: 300px;"/>
                                        <el-select v-if="fp.attrValued == 2" size="small" class="ew" placeholder="属性" v-model="fp.attr" filterable remote remote-show-suffix :remote-method="attributesLoad" :loading="atLoading" >
                                            <el-option v-for="attr in localAttributes" :label="attr.name" :value="attr.id" />
                                        </el-select>
                                    </el-form-item>
                                </el-row>
                                <el-row>
                                    <el-form-item label="条件">
                                        <bcomSortCondEditor v-for="(acond, aindex)  in fp.conds" :cond="acond" :delCond="() => delCond(fp.conds, aindex)"  />
                                            <el-popover placement="right" trigger="click" >
                                                <template #reference>
                                                    <el-button size="small" icon="plus" circle type="primary" class="btn-plus"></el-button>
                                                </template>
                                                <el-select size="small" @change="(op) => addCond(fp, 'conds', op)" placeholder="选择条件" >
                                                    <el-option v-for="item in condOptions" :key="item.value" :label="item.label" :value="item.value" />
                                                </el-select>
                                            </el-popover>
                                    </el-form-item>
                                </el-row>
                            </div>
                            <el-row v-if="ded.cond_type==6">
                                <el-col :span="8">
                                    <el-form-item label="建筑面积">
                                        <el-select class="ew"  placeholder="类型" v-model="ded.mj_type">
                                            <el-option label="首层建筑面积" :value="1" />
                                            <el-option label="最大地下室建筑面积" :value="2" />
                                            <el-option label="首层与地下建筑面积合并" :value="3" />
                                        </el-select>
                                    </el-form-item>
                                </el-col>
                                <el-col :span="8">
                                    <el-form-item label="外延(mm)">
                                        <el-input-number v-model="ded.extension" style="width: 100px;"/>
                                    </el-form-item>
                                </el-col>
                            </el-row>

                            <el-row v-if="ded.cond_type==7">
                                <el-col :span="8">
                                    <el-form-item label="保温层长度类型">
                                        <el-select class="ew"  placeholder="长度类型" v-model="ded.cd_type">
                                            <el-option label="墙边线" :value="1" />
                                            <el-option label="中心线" :value="2" />
                                        </el-select>
                                    </el-form-item>
                                </el-col>
                            </el-row>
                            <el-row v-if="ded.cond_type==8">
                                <el-col :span="8">
                                    <el-form-item label="条形基础扣减类型">
                                         <el-select class="ew"  placeholder="扣减类型" v-model="ded.cd_type">
                                            <el-option label="按实扣减" :value="1" />
                                            <el-option label="按最小宽度扣减" :value="2" />
                                        </el-select>    
                                    </el-form-item>
                                </el-col>
                            </el-row>
                             <el-row v-if="ded.cond_type==9" >
                                <el-col :span="8">
                                    <el-form-item label="风管堵头是否计算">
                                        <el-select class="ew" placeholder="是否计算" v-model="ded.cd_type">
                                            <el-option label="否" :value="false" />
                                            <el-option label="是" :value="true" />
                                        </el-select>
                                    </el-form-item>
                                </el-col>
                            </el-row>
                        </el-form>
                    </el-tab-pane>
                </el-tabs>
                <el-popover width="220" title="是否删除?" :visible="delConfirmShow" placement="bottom-end" :offset="22" :virtual-ref="delConfirmRef" virtual-triggering :persistent="false"  >
                    <div style="text-align: right;">
                        <el-button type="primary" size="small" @click="()=>{condDefFunc();delConfirmShow=false;}">确定</el-button>
                        <el-button size="small" @click="delConfirmShow=false">取消</el-button>
                    </div>
                </el-popover>
            </el-form-item>
            <el-form-item label="描述">
                <el-input v-model="currentRow.remarks" style="width: 600px;"></el-input>
            </el-form-item>
            <el-form-item>
                <el-button type="primary" @click="saveData(currentRow)">{{ this.currentRow.id ? '保存' : '新增' }}</el-button>
                <el-button @click="editCancel">取消</el-button>
            </el-form-item>
        </el-form>
</template>
<script>
import Sortable from 'sortablejs'
import bcomSortCondEditor from './BComSortCondEditor.vue'
import {condOptions,ruleTypes,addCond,delCond,attrValuedTypes} from '@/calcRule/calc_config'

export default {
    name: 'BComCalcRuleEditor',
    inject:['bidAttributes','bidAttributesMap','bcomOptions','calcBComs','thicknessTables','allFormulas'],
    components: { bcomSortCondEditor },
    props: {
        saveData: Function,
        editCancel: Function
    },
    setup() {
        const cprops = { multiple: true};
        return { cprops,condOptions,ruleTypes,addCond,delCond,attrValuedTypes }
    },

    data() {
        return {
            /**
             * 当前数据
             */
            currentRow: {},
            /**
             * 当前编辑tab
             */
            editableTab:0,
            /**
             * 是否显示删除框
             */
            delConfirmShow:false,
            /**
             * 虚拟删除
             */
            delConfirmRef:null,
            /**
             * 删除方法
             */
            condDefFunc:null,
            /**
             * 属性
             */
            localAttributes:null,
            /**
             * 属性加载中
             */
            atLoading:false,
            /**
             * 当前构件
             */
            currentBComs:[],
            /**
             * 砌体墙厚度表数据
             */
            tableData:[],
            /**
             * 当前配置公式
             */
            currentFormulaMathml:null
        }
    },
    mounted(){
        this.tabsDrop();        
    },
    methods: {
        /**
         * 加载属性
         */
        attributesLoad(keyword) {
            this.atLoading = true;
            if (keyword) {
                this.localAttributes = this.bidAttributes.filter(c => c.name.toLowerCase().includes(keyword.toLowerCase())).sort(this.lengtSort);
            }
            else {
                this.localAttributes = this.bidAttributes;// this.cond.attr?this.bidAttributes.filter(c=>c.id==this.cond.attr):[]; 
            }
            this.atLoading = false;
        },
        /**
         * 修改当前显示数据
         * @param {object} row 
         */
         async changeData(row){
            this.currentRow=row;
            if(!this.currentRow.calc_settings)this.currentRow.calc_settings=[];
            this.currentBComs=row.bcom?.split(',')?.map(d=>[row.classification,Number(d)]);
            if(this. currentRow.calc_settings.length>0){
                let temp_setting=this.currentRow.calc_settings[0];
                switch (temp_setting.cond_type) {
                    case 4:
                        this.tableData=this.thicknessTables.find(d=>d.value==temp_setting.thicknessTable)?.trans_data;
                        break;
                    case 5:
                        let temp_formula=this.allFormulas.find(d=>d.id==temp_setting.formula);
                        this.currentFormulaMathml=temp_formula.mathml;
                        break;
                    default:
                        this.tableData=null;
                        break;
                }
            }
            await this.attributesLoad();
        },  
        /**
         * 弹出删除确认框
         * @param {Object} btn 
         * @param {Function} delFunc 
         */
        showTabDelConfirm(btn,delFunc){
            this.delConfirmRef=btn;
            this.delConfirmShow=true;
            this.condDefFunc=delFunc;
        },
        /**
         * 添加新tab
         */
        handleTabsAdd(){
            if(this.currentRow?.calc_settings==undefined ||this.currentRow.calc_settings==null)this.currentRow.calc_settings=[];
            this.currentRow.calc_settings.push({cond_type:1,attr_conds:[],attr:null,factor:null});
            this.editableTab=this.currentRow.calc_settings.length-1;
        },
         /**
         * 拖动
         */
         async tabsDrop() {
            this.sortable=Sortable.create(
                document.querySelector('.bcom-tabs .el-tabs__nav'),
                {
                    handle:'.i-rank',
                    animation:150,
                    onEnd:({newIndex,oldIndex})=>this.changeTabsOrder(newIndex,oldIndex)
                }
            );
        },
        /**
         * 修改tabs排序
         * @param {Number} newIndex 
         * @param {Number} oldIndex 
         */
        changeTabsOrder(newIndex,oldIndex){
            if(newIndex==oldIndex)return false;
            this.currentRow.calc_settings.splice(newIndex,0,this.currentRow.calc_settings.splice(oldIndex,1)[0]);
            this.editableTab=newIndex;
        },
        /**
         * 删除tab
         * @param {Number} tabIndex 
         */
        delTabs(tabIndex){
            this.currentRow.calc_settings.splice(tabIndex,1);
            if(this.editableTab==tabIndex&&tabIndex>=this.currentRow.calc_settings.length-1){
                this.editableTab=this.currentRow.calc_settings.length-1;
            }
        },
        /**
         * 构件修改
         */
        bcomChange(){
            if(this.currentBComs?.length>0){
                this.currentRow.classification=this.currentBComs[0][0];
                let temp_bcoms= this.currentBComs.flatMap(d=>d[1]);
                this.currentRow.bcom=temp_bcoms.join(',');
            }
            else{
                this.currentRow.classification=null;
                this.currentRow.bcom=null;
            }
        },
        /**
         * 条件变更
         * @param {Number} index 
         * @param {Number} val 
         */
        condTypeChange(index,val){
            this.currentFormulaMathml=null;
            this.tableData=[];
            let temp_attr=this.currentRow.calc_settings[index].attr;
            switch (val) {
                //计算系数
                case 1:
                    this.currentRow.calc_settings[index]={cond_type:val,attr:temp_attr,factor:null};
                    break;
                //属性转换
                case 2:
                    this.currentRow.calc_settings[index]={cond_type:val,attr:temp_attr,trans_attr:null,factor:null};
                    break;
                //延伸 加长
                case 3:
                    this.currentRow.calc_settings[index]={cond_type:val,attr:temp_attr,entended:null,entend_attr:null};
                    break;
                //砌体墙厚度计算表
                case 4:
                    this.currentRow.calc_settings[index]={cond_type:val,attr:temp_attr,thicknessTable:null};
                    // this.thicknessTableChange(1);
                    break;
                  //计算公式
                case 5:
                    this.currentRow.calc_settings[index]={cond_type:val,attr:temp_attr,formula:null,fparams:[]};
                    break;
                //平整场地 单层建筑面积
                case 6:
                    this.currentRow.calc_settings[index]={cond_type:val,attr:temp_attr,mj_type:null,extension:0};
                    break;
                //保温层计算长度
                case 7:
                    this.currentRow.calc_settings[index]={cond_type:val,attr:temp_attr,cd_type:null,extension:0};
                    break;
                default:
                    break;
            }
        },
        /**
         * 变更厚度表
         * @param {Number} val 
         */
        thicknessTableChange(val){
            this.tableData=this.thicknessTables.find(d=>d.value==val).trans_data;
        },
        formulaChange(val,obj){
            let temp_formula=this.allFormulas.find(d=>d.id==val);
            this.currentFormulaMathml=temp_formula.mathml;
            let temp_fparams=temp_formula.fparams.split(',');
            if(temp_fparams?.length>0){
                obj.fparams=temp_fparams.map(d=>{return {name:d,conds:[]}});
            }
        }
    },
}
</script>
<style scoped>
.bcom-tabs{
    width: 100%;
}
.btn-plus{
    margin-left: 5px;
}
.bcom-tabs .el-form-item{
    margin-bottom: 10px;
}
.deductText{
    margin-left: 20px;
    margin-right: 10px;
}

.bcom-tabs .i-rank{
    width: 1em;
    height: 1em;
    margin-right: 3px;
    cursor:grab;
}

:deep(.bcom-tabs .el-tabs__new-tab){
    margin-right:12px;
}
:deep(.bcoms-cascader){
    width: 300px;
}
.cond-tr{
    padding:4px;
    border:1px dashed var(--el-border-color);
    border-radius: 4px;
}
.cond-tr+.cond-tr{
    margin-top: 5px;
}
</style>